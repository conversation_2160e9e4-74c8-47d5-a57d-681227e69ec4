import json
import time
import sqlite3 # Importera SQLite-modulen
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException, NoSuchElementException

# --- Konfiguration ---
CHROME_DRIVER_PATH = '/usr/bin/chromedriver'
DATABASE_NAME = 'power_stock.db' # Namn på SQLite-databasfilen

PRODUCT_IDS_TO_CHECK = [
    "1935540", "3481365", "2193593", "2193594", "2193596",
    # Lägg till fler produkt-ID:n från din homepodProducts-lista här
    # "2193591", "2193592", "2193590", "1898801"
]
DELAY_BETWEEN_REQUESTS = 2
# ---------------------

def setup_driver():
    # (Din setup_driver funktion - oförändrad)
    print("Konfigurerar WebDriver...")
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    try:
        service = Service(executable_path=CHROME_DRIVER_PATH)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        print("WebDriver konfigurerad och startad.")
        return driver
    except WebDriverException as e:
        print(f"FEL vid konfigurering av WebDriver: {e}")
        return None

def fetch_product_data_via_get(driver, product_id):
    # (Din fetch_product_data_via_get funktion - oförändrad)
    if not driver:
        print(f"WebDriver är inte tillgänglig för produkt {product_id}.")
        return None
    api_url = f"https://power.se/api/sparoute/x/p-{product_id}/"
    # print(f"Försöker hämta data för produkt-ID {product_id} direkt från URL: {api_url}") # Mindre pratig
    try:
        driver.get(api_url)
        try:
            pre_element = driver.find_element("tag name", "pre")
            json_text_from_pre = pre_element.text
            data = json.loads(json_text_from_pre)
        except NoSuchElementException:
            page_source = driver.page_source
            if page_source.strip().startswith("{") and page_source.strip().endswith("}"):
                data = json.loads(page_source)
            else:
                # print(f"Sidkällan för {product_id} verkar inte vara ren JSON.") # Mindre pratig
                return {"error": "NotJSON", "message": "Sidkällan från driver.get() var inte JSON."}
        return data
    except json.JSONDecodeError as e:
        print(f"FEL vid JSON-parsning för produkt {product_id}: {e}")
        return {"error": "JSONDecodeError", "message": str(e)}
    except Exception as e:
        print(f"Oväntat FEL under fetch_product_data_via_get för produkt {product_id}: {e}")
        return {"error": "UnexpectedException", "message": str(e)}

# --- Databasfunktioner ---
def init_db(db_name):
    """Skapar databasen och tabellen om de inte redan finns."""
    conn = sqlite3.connect(db_name)
    cursor = conn.cursor()
    # Tabell för produktinformation (om du vill spara titlar etc.)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            productId TEXT PRIMARY KEY,
            title TEXT,
            last_checked DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    # Tabell för lagerstatus
    # En rad per produkt per butik
    # Vi kan rensa och fylla på denna varje gång, eller uppdatera
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_levels (
            productId TEXT,
            storeId TEXT, -- Butiks-ID är ofta strängar i API:er
            stockCount INTEGER,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (productId, storeId),
            FOREIGN KEY (productId) REFERENCES products (productId)
        )
    ''')
    conn.commit()
    conn.close()
    print(f"Databas '{db_name}' initialiserad/verifierad.")

def update_stock_data_in_db(db_name, product_id, product_title, stocks_data):
    """Rensar gammal lagerstatus för en produkt och lägger in ny."""
    conn = sqlite3.connect(db_name)
    cursor = conn.cursor()

    # Uppdatera eller infoga produktinformation
    cursor.execute('''
        INSERT INTO products (productId, title, last_checked) 
        VALUES (?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(productId) DO UPDATE SET
            title=excluded.title,
            last_checked=CURRENT_TIMESTAMP
    ''', (product_id, product_title))

    # Rensa gammal lagerstatus för denna produkt
    cursor.execute("DELETE FROM stock_levels WHERE productId = ?", (product_id,))

    # Lägg in ny lagerstatus
    if stocks_data: # stocks_data är ProductStoreStocks-objektet
        for store_id, stock_info in stocks_data.items():
            stock_count = stock_info.get("Stock", 0)
            # Spara bara om det finns lager, eller spara alla (även 0)
            # if stock_count > 0: 
            cursor.execute('''
                INSERT INTO stock_levels (productId, storeId, stockCount, last_updated)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (product_id, store_id, stock_count))
        print(f"Lagerstatus för produkt {product_id} ({product_title}) uppdaterad i databasen.")
    else:
        print(f"Ingen lagerdata (stocks_data) att spara för produkt {product_id}.")
        
    conn.commit()
    conn.close()
# -------------------------

if __name__ == "__main__":
    init_db(DATABASE_NAME) # Se till att databasen och tabellerna finns
    
    driver = setup_driver()
    if driver:
        try:
            for product_id_str in PRODUCT_IDS_TO_CHECK:
                print(f"\n--- Bearbetar produkt: {product_id_str} ---")
                api_response_data = fetch_product_data_via_get(driver, product_id_str)

                if api_response_data and not api_response_data.get("error"):
                    product_title_from_api = api_response_data.get("Model", {}).get("ExtendedProduct", {}).get("Name", f"Okänd produkt {product_id_str}")
                    product_store_stocks = api_response_data.get("Model", {}).get("ExtendedProduct", {}).get("ProductStoreStocks")
                    
                    if product_store_stocks is not None: # Kontrollera att ProductStoreStocks faktiskt finns
                        print(f"Hämtade data för '{product_title_from_api}'. Sparar till databas...")
                        update_stock_data_in_db(DATABASE_NAME, product_id_str, product_title_from_api, product_store_stocks)
                    else:
                        print(f"Mottagen data för {product_id_str} saknar ProductStoreStocks.")
                        # Du kan välja att spara produktinfo även om lagerinfo saknas
                        update_stock_data_in_db(DATABASE_NAME, product_id_str, product_title_from_api, None)


                elif api_response_data and api_response_data.get("error"):
                    print(f"FEL mottogs vid datahämtning för {product_id_str}: {api_response_data.get('message')}")
                    # Spara felinformation om du vill
                    # update_stock_data_in_db(DATABASE_NAME, product_id_str, f"Produkt {product_id_str} (FEL)", {"error": api_response_data.get('message')})
                else:
                    print(f"Ingen data eller oväntat format mottogs för produkt {product_id_str}.")
                
                print(f"Väntar {DELAY_BETWEEN_REQUESTS} sekunder...")
                time.sleep(DELAY_BETWEEN_REQUESTS)

            print("\n\n--- Datainsamling slutförd. Datan bör nu finnas i '{DATABASE_NAME}' ---")

        finally:
            print("\nStänger WebDriver...")
            driver.quit()
            print("WebDriver stängd.")
    else:
        print("Kunde inte starta WebDriver. Avslutar skript.")
