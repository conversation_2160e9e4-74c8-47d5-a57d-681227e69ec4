from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

print("Försöker starta Selenium med ChromeDriver...")

chrome_options = Options()
chrome_options.add_argument("--headless")
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
# chrome_options.add_argument("--remote-debugging-port=9222") 
service = Service(executable_path='/usr/bin/chromedriver') # Eller '/usr/lib/chromium-browser/chromedriver'
driver = webdriver.Chrome(service=service, options=chrome_options)
try:
#    driver = webdriver.Chrome(options=chrome_options) 
# Inom ditt Selenium-skript

driver.get("about:blank") # Öppna en tom sida
product_id = "2193593" # Exempel
api_url = f"https://power.se/api/sparoute/x/p-{product_id}/"
script = f"""
return fetch('{api_url}')
    .then(response => response.json())
    .catch(error => {{ return {{'error': error.toString()}} }});
"""
json_data = driver.execute_script(script)
# Nu innehåller json_data det parsade JSON-objektet eller ett felobjekt

    print("WebDriver startad. Försöker öppna en sida...")
    driver.get("https://www.example.com")
    print(f"Sidans titel: {driver.title}")
    driver.quit()
    print("Selenium-test lyckades och webbläsaren stängdes.")

except Exception as e:
    print(f"Ett fel uppstod under Selenium-testet: {e}")
    print("Möjliga orsaker/lösningar:")
    print("- ChromeDriver är inte installerad korrekt eller inte i systemets PATH.")
    print("- Versionen av ChromeDriver matchar inte versionen av Chromium Browser.")
    print("  Kör 'chromium-browser --version' och 'chromedriver --version' för att jämföra.")
    print("- Du kan behöva specificera sökvägen till chromedriver explicit i Service-objektet.")
    print("  Vanliga sökvägar på Pi: /usr/bin/chromedriver eller /usr/lib/chromium-browser/chromedriver")
