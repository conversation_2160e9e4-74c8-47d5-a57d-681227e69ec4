// script.js (Version med Avancerad vy som standard och tre lagerfilter)

// --- Globala variabler ---
const stores = [
    {"storeId": "7152", "namn": "POWER Torpavallen", "stad": "Göteborg", "adress": "Torpavallsgatan 4D", "lat": 57.6792, "lng": 11.9894},
    {"storeId": "7150", "namn": "POWER Högsbo", "stad": "Västra Frölunda", "adress": "Lona Knapes gata 1", "lat": 57.6348, "lng": 11.9059},
    {"storeId": "7151", "namn": "POWER Bäckebol", "stad": "Hisings Backa", "adress": "Transportgatan 19", "lat": 57.7461, "lng": 11.9094},
    {"storeId": "7156", "namn": "POW<PERSON> Borås", "stad": "<PERSON><PERSON><PERSON><PERSON>", "adress": "Ålgårdsvägen 11", "lat": 57.7210, "lng": 12.9401},
    {"storeId": "7125", "namn": "POWER Jönköping", "stad": "Jönköping", "adress": "Solåsvägen 4A", "lat": 57.7826, "lng": 14.1618},
    {"storeId": "7155", "namn": "POWER Skövde", "stad": "Skövde", "adress": "Jonstorpsgatan 3C", "lat": 58.3875, "lng": 13.8458},
    {"storeId": "7147", "namn": "POWER Helsingborg Väla", "stad": "Ödåkra", "adress": "Marknadsvägen 5", "lat": 56.0776, "lng": 12.7441},
    {"storeId": "7170", "namn": "POWER Västerås", "stad": "Västerås", "adress": "Hallsta Gårdsgata 7", "lat": 59.6099, "lng": 16.5448},
    {"storeId": "7148", "namn": "POWER Lund", "stad": "Lund", "adress": "Avtalsvägen 2", "lat": 55.7047, "lng": 13.2900},
    {"storeId": "7149", "namn": "POWER Kristianstad", "stad": "Kristianstad", "adress": "Fundamentgatan 1", "lat": 56.0280, "lng": 14.1567},
    {"storeId": "7153", "namn": "POWER Linköping", "stad": "Linköping", "adress": "Björkgatan 4", "lat": 58.4108, "lng": 15.6214},
    {"storeId": "7154", "namn": "POWER Norrköping", "stad": "Norrköping", "adress": "Koppargatan 30", "lat": 58.5877, "lng": 16.1924},
    {"storeId": "7146", "namn": "POWER Malmö Svågertorp", "stad": "Malmö", "adress": "Nornegatan 8", "lat": 55.5636, "lng": 12.9719},
    {"storeId": "7157", "namn": "POWER Uppsala", "stad": "Uppsala", "adress": "Stångjärnsgatan 10", "lat": 59.8586, "lng": 17.6389},
    {"storeId": "7158", "namn": "POWER Örebro", "stad": "Örebro", "adress": "Bettorpsgatan 4", "lat": 59.2741, "lng": 15.2066},
    {"storeId": "7159", "namn": "POWER Sundsvall", "stad": "Sundsvall", "adress": "Norra Förmansvägen 18", "lat": 62.3908, "lng": 17.3069},
    {"storeId": "7160", "namn": "POWER Gävle", "stad": "Gävle", "adress": "Ingenjörsgatan 2", "lat": 60.6749, "lng": 17.1413},
    {"storeId": "7161", "namn": "POWER Stockholm Kungens Kurva", "stad": "Kungens Kurva", "adress": "Geometrivägen 1", "lat": 59.2465, "lng": 17.9414},
    {"storeId": "7162", "namn": "POWER Stockholm Barkarby", "stad": "Järfälla", "adress": "Herrestavägen 20", "lat": 59.4142, "lng": 17.8907}
];

let ALL_PRODUCTS_FROM_API = []; 
let displayedProductsCache = []; 

let userLocation = null;
let selectedStores = []; 
let currentStoreFilterMode = 'closest'; 
let currentSelectedCategory = null; 
let stockStatusFilter = 'in_stock'; // NY: 'in_stock', 'all', 'out_of_stock'
let currentDisplayMode = 'advanced'; // NY: Startar i avancerat läge
let currentSortOption = 'stock_desc'; // NY: Sorteringsalternativ
let searchQuery = ''; // NY: Sökfråga

// DOM-element referenser
const locationStatusDiv = document.getElementById('location-status-text');
const storeSelectorDiv = document.getElementById('store-selector');
const loadButton = document.getElementById('load-button');
const loadingDiv = document.getElementById('loading');
const errorDiv = document.getElementById('error');
const resultsDiv = document.getElementById('results');
const categoryButtonsDiv = document.getElementById('category-buttons');
const viewModeToggleButton = document.getElementById('view-mode-toggle');
const summaryContentDiv = document.getElementById('summary-content');
const summarySectionDiv = document.getElementById('summary-section');
const searchInput = document.getElementById('search-input');
const sortDropdown = document.getElementById('sort-dropdown');
const clearSearchButton = document.getElementById('clear-search');
        
// === VIKTIGT: Byt till din AKTUELLA Cloudflare Tunnel URL här! ===
const API_BASE_URL = 'https://api.rytterfalk.com'; // ERSÄTT OM DEN ÄNDRATS!
// ======================================================

const MODEL_KEYWORDS = {
    "Mac": ["MacBook Air 13", "MacBook Air 15", "MacBook Pro 13", "MacBook Pro 14", "MacBook Pro 16", "iMac 24", "iMac 27", "Mac mini", "Mac Studio", "Mac Pro", "MacBook Air", "MacBook Pro", "iMac", "M1", "M2", "M3", "M4"],
    "iPhone": ["iPhone 16 Pro Max", "iPhone 16 Pro", "iPhone 16 Plus", "iPhone 16e", "iPhone 16", "iPhone 15 Pro Max", "iPhone 15 Pro", "iPhone 15 Plus", "iPhone 15", "iPhone SE", "iPhone 14", "iPhone 13", "Pro Max", "Pro", "Plus"],
    "iPad": ["iPad Pro 13", "iPad Pro 12.9", "iPad Pro 11", "iPad Air 13", "iPad Air 11", "iPad Air", "iPad Mini", "iPad 10.9", "iPad (A16)","iPad 10.2", "iPad Pro", "iPad"],
    "Watch": ["Apple Watch Series 10 GPS + Cell 46 mm", "Apple Watch Series 10 GPS 46 mm",  "Apple Watch Series 10 GPS + Cell 42 mm", "Apple Watch Series 10 GPS 42", "Apple Watch Series 10", "Apple Watch Ultra 2", "Apple Watch Ultra", "Apple Watch Series 9", "Apple Watch Series 8", "Apple Watch Series 7", "Apple Watch SE GPS + Cell", "Apple Watch SE GPS","Apple Watch SE", "49mm", "45mm", "44mm", "41mm", "40mm", "42mm", "46mm"],
    "AirPods": ["AirPods Max", "AirPods Pro (2nd gen)", "AirPods Pro", "AirPods (3rd gen)", "AirPods (2nd gen)", "AirPods"],
    "ATV & HomePods": ["Apple TV 4K", "HomePod (2nd Gen)", "HomePod Mini", "Apple TV", "HomePod"],
    "Tillbehör": ["Magic Keyboard", "Magic Mouse", "Apple Pencil", "Smart Folio", "Adapter", "Kabel", "Laddare"]
};

// --- FUNKTIONER ---

function getProductSubGroup(title, category) {
    if (title && category && MODEL_KEYWORDS[category]) {
        for (const keyword of MODEL_KEYWORDS[category]) {
            if (title.toLowerCase().includes(keyword.toLowerCase())) {
                return keyword; 
            }
        }
    }
    if (title && category === "Mac") { 
        if (title.toLowerCase().includes("macbook air")) return "MacBook Air";
        if (title.toLowerCase().includes("macbook pro")) return "MacBook Pro";
        if (title.toLowerCase().includes("imac")) return "iMac";
        if (title.toLowerCase().includes("mac mini")) return "Mac mini";
        if (title.toLowerCase().includes("mac studio")) return "Mac Studio";
    }
    return null; 
}

async function initializeApp() {
    console.log("initializeApp called");
    if(errorDiv) errorDiv.style.display = 'none';
    try {
        const productsApiUrl = `${API_BASE_URL}/api/products`;
        console.log("Hämtar produktlista från:", productsApiUrl);
        const response = await fetch(productsApiUrl);
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Kunde inte hämta produktlistan: ${response.status}. Svar: ${errorText.substring(0,100)}`);
        }
        const productsFromApi = await response.json();
        console.log("Produktlista mottagen från API:", productsFromApi.length, "produkter");

        if (productsFromApi && productsFromApi.length > 0) {
            ALL_PRODUCTS_FROM_API = productsFromApi.map(p => ({
                productId: p.productId ? p.productId.toString() : null,
                title: p.title || "Okänd Titel",
                category: p.categoryName || "Okategoriserad",
                ean: p.ean, 
                sku: p.sku, 
                price: p.price 
            })).filter(p => p.productId);
        } else { 
            console.warn("Produktlistan från API var tom eller ogiltig.");
            ALL_PRODUCTS_FROM_API = [];
        }
    } catch (err) {
        console.error("FEL vid hämtning av produktlista:", err);
        if(errorDiv) {
            errorDiv.textContent = `Kunde inte ladda den initiala produktlistan: ${err.message}`;
            errorDiv.style.display = 'block';
        }
        ALL_PRODUCTS_FROM_API = [];
    }
    renderCategoryButtons(); 
    selectCategory(null); 
    getUserLocation();       
}

function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; const dLat = (lat2 - lat1) * Math.PI / 180; const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); return R * c;
}

function updateActiveButton(buttonClassCSSSelector, newActiveButtonId) {
    const buttonsInGroup = document.querySelectorAll(buttonClassCSSSelector);
    buttonsInGroup.forEach(btn => btn.classList.remove('active'));
    const activeButton = document.getElementById(newActiveButtonId);
    if (activeButton) activeButton.classList.add('active');
}

function renderCategoryButtons() {
    if (!categoryButtonsDiv) return;
    const uniqueCategories = [...new Set(ALL_PRODUCTS_FROM_API.map(p => p.category).filter(cat => cat))];
    const desiredCategoryOrder = ["Mac", "iPad", "iPhone", "Watch", "AirPods", "ATV & HomePods", "Tillbehör"];
    const sortedCategories = uniqueCategories.sort((a, b) => {
        const indexA = desiredCategoryOrder.indexOf(a); const indexB = desiredCategoryOrder.indexOf(b);
        if (indexA !== -1 && indexB !== -1) return indexA - indexB;
        if (indexA !== -1) return -1; if (indexB !== -1) return 1;
        return a.localeCompare(b);
    });
    categoryButtonsDiv.innerHTML = '';
    ['Alla Produkter', ...sortedCategories].forEach((categoryText) => {
        const categoryValue = categoryText === 'Alla Produkter' ? null : categoryText;
        const btn = document.createElement('button'); btn.textContent = categoryText;
        const btnIdSuffix = categoryValue ? categoryValue.replace(/\s|&|\//g, '-') : 'all';
        btn.id = `btn-cat-${btnIdSuffix}`;
        btn.onclick = () => selectCategory(categoryValue);
        if (currentSelectedCategory === categoryValue) btn.classList.add('active');
        categoryButtonsDiv.appendChild(btn);
    });
}

function selectCategory(categoryName) {
    currentSelectedCategory = categoryName;
    renderCategoryButtons(); 
    if (selectedStores.length > 0 || currentStoreFilterMode !== 'user_selected') {
        loadStockData();
    } else if (currentStoreFilterMode === 'user_selected' && resultsDiv) {
        resultsDiv.innerHTML = `<div style="grid-column: 1/-1; text-align:center; color:white; padding:50px;"><h3>Välj butiker för '${categoryName || "Alla produkter"}'</h3></div>`;
    }
}

function getUserLocation() {
    const btnClosest = document.getElementById('btn-closest');
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (p) => { userLocation = {lat:p.coords.latitude, lng:p.coords.longitude}; if(locationStatusDiv)locationStatusDiv.textContent=`Position hittad.`; setStoreFilter('closest',true); },
            (e) => { console.error('Geo err:',e); if(locationStatusDiv)locationStatusDiv.textContent='Kunde inte hitta pos.'; if(btnClosest) btnClosest.textContent = 'Min närmaste (Position?)'; setStoreFilter('user_selected',false); }
        );
    } else { if(locationStatusDiv)locationStatusDiv.textContent='Geo stöds ej. Välj manuellt.'; if(btnClosest) btnClosest.textContent = 'Min närmaste (Geo stöds ej)'; setStoreFilter('user_selected',false); }
}

function setStoreFilter(mode, shouldLoadData = true) {
    currentStoreFilterMode = mode;
    updateActiveButton('.filter-buttons button', `btn-${mode}`);
    selectedStores = [];
    const btnClosest = document.getElementById('btn-closest');
    const btnNearby = document.getElementById('btn-nearby');
    if (mode !== 'closest' && btnClosest) btnClosest.textContent = 'Min närmaste';
    if (mode !== 'nearby' && btnNearby) btnNearby.textContent = 'Närliggande (50km)';

    if (!userLocation && (mode === 'closest' || mode === 'nearby')) {
        if(locationStatusDiv)locationStatusDiv.textContent = 'Position behövs.';
        currentStoreFilterMode = 'user_selected'; 
        updateActiveButton('.filter-buttons button', 'btn-user_selected');
         if (btnClosest) btnClosest.textContent = 'Min närmaste (Position?)';
    }

    if (currentStoreFilterMode === 'closest' && userLocation) {
        if (stores.length === 0) { if(btnClosest) btnClosest.textContent = 'Närmaste (0)'; if(locationStatusDiv)locationStatusDiv.textContent = 'Inga butiker.'; return; }
        let closest = stores.reduce((prev, curr) => (calculateDistance(userLocation.lat, userLocation.lng, curr.lat, curr.lng) < calculateDistance(userLocation.lat, userLocation.lng, prev.lat, prev.lng) ? curr : prev), stores[0] || null);
        if (closest) { 
            selectedStores = [closest.storeId.toString()]; 
            if (btnClosest) btnClosest.textContent = `Visar: ${closest.namn}`;
            if(locationStatusDiv)locationStatusDiv.textContent = `(${calculateDistance(userLocation.lat,userLocation.lng,closest.lat,closest.lng).toFixed(1)} km)`;
        } else {
            if (btnClosest) btnClosest.textContent = 'Närmaste (Hittades ej)';
            if(locationStatusDiv)locationStatusDiv.textContent = 'Kunde inte identifiera närmaste.';
        }
    } else if (currentStoreFilterMode === 'nearby' && userLocation) {
        const r=50;selectedStores=stores.filter(s=>calculateDistance(userLocation.lat,userLocation.lng,s.lat,s.lng)<=r).map(s=>s.storeId.toString());
        if(btnNearby)btnNearby.textContent=`Närliggande (${selectedStores.length}st)`;
        if(locationStatusDiv)locationStatusDiv.textContent=selectedStores.length>0?`Inom ${r}km.`:`Inga butiker inom ${r}km.`;
    } else if (currentStoreFilterMode === 'all_stores') {
         selectedStores = stores.map(s => s.storeId.toString());
         if(locationStatusDiv)locationStatusDiv.textContent = `Visar för alla ${selectedStores.length} butiker.`;
    } else { // user_selected
        if(locationStatusDiv)locationStatusDiv.textContent = 'Anpassat urval: Välj butiker.';
    }
    displayStores(); 
    updateLoadButton();
    if (shouldLoadData && selectedStores.length > 0) {
        loadStockData();
    } else if (shouldLoadData && selectedStores.length === 0 && currentStoreFilterMode !== 'user_selected') {
        if(resultsDiv) resultsDiv.innerHTML = `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga butiker.</h3></div>`;
        if(loadButton) loadButton.disabled = true; 
    } else if (currentStoreFilterMode === 'user_selected' && selectedStores.length === 0 && shouldLoadData) {
        if(resultsDiv) resultsDiv.innerHTML = `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Välj butiker.</h3></div>`;
    }
}

function displayStores() {
    if (currentStoreFilterMode !== 'user_selected') {
        if(storeSelectorDiv) storeSelectorDiv.style.display = 'none'; return;
    }
    if(!storeSelectorDiv) return; 
    storeSelectorDiv.style.display = 'grid';
    let sortedStores = [...stores];
    if (userLocation) {
        sortedStores.forEach(s => s.distance = calculateDistance(userLocation.lat, userLocation.lng, s.lat, s.lng));
        sortedStores.sort((a, b) => a.distance - b.distance);
    }
    storeSelectorDiv.innerHTML = ''; 
    sortedStores.forEach(store => {
        const storeCard = document.createElement('div');const sIdStr=store.storeId.toString();
        storeCard.className = `store-card ${selectedStores.includes(sIdStr)?'selected':''}`;
        storeCard.dataset.storeId = sIdStr; storeCard.onclick = () => toggleStore(store.storeId);
        let dText = store.distance ? `<div class="distance">${store.distance.toFixed(1)} km</div>` : '';
        storeCard.innerHTML = `<div class="store-name">${store.namn}</div><div class="store-address">${store.adress ? store.adress + ', ' : ''}${store.stad}</div>${dText}`;
        storeSelectorDiv.appendChild(storeCard);
    });
    updateLoadButton();
}

function toggleStore(storeId) {
    const sIdStr=storeId.toString();const idx=selectedStores.indexOf(sIdStr);
    if(idx>-1)selectedStores.splice(idx,1);else selectedStores.push(sIdStr);
    const card=storeSelectorDiv.querySelector(`[data-store-id="${sIdStr}"]`);
    if(card)card.classList.toggle('selected');
    if(selectedStores.length>0)loadStockData();
    else{if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Välj butiker.</h3></div>`;}
    updateLoadButton(); 
}

function updateLoadButton() {
    if (!loadButton) return; 
    if (currentStoreFilterMode === 'closest') {
        const cS=selectedStores.length>0?stores.find(s=>s.storeId.toString()===selectedStores[0]):null;
        loadButton.textContent=cS?`Uppdatera för ${cS.namn}`:(userLocation?'Ingen närmaste':'Hitta närmaste');
    } else if (currentStoreFilterMode === 'nearby') {
        loadButton.textContent=`Uppdatera för närliggande (${selectedStores.length}st)`;
    } else if (currentStoreFilterMode === 'all_stores') {
        loadButton.textContent=`Uppdatera för alla ${selectedStores.length} butiker`;
    } else { 
        loadButton.textContent=selectedStores.length===0?'Välj butiker':`Ladda lager (${selectedStores.length} valda)`;
    }
    loadButton.disabled = selectedStores.length === 0;
}

// --- NY/MODIFIERAD FUNKTIONALITET ---

function toggleDisplayMode() {
    currentDisplayMode = (currentDisplayMode === 'compact') ? 'advanced' : 'compact';
    if (viewModeToggleButton) {
        viewModeToggleButton.textContent = currentDisplayMode === 'advanced' ? 'Kompakt vy' : 'Avancerad vy';
        viewModeToggleButton.classList.toggle('active', currentDisplayMode === 'advanced');
    }
    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        updateSummaryView(displayedProductsCache);
    }
}

function setStockStatusFilter(mode) {
    stockStatusFilter = mode;
    updateActiveButton('.filter-buttons-compact button', `btn-stock-${mode}`);
    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        updateSummaryView(displayedProductsCache);
    } else if (selectedStores.length > 0) {
        loadStockData();
    }
}

function setSortOption(sortOption) {
    currentSortOption = sortOption;
    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        updateSummaryView(displayedProductsCache);
    }
}

function performSearch() {
    searchQuery = searchInput ? searchInput.value.toLowerCase().trim() : '';

    // Visa/dölj rensa-knappen
    if (clearSearchButton) {
        clearSearchButton.style.display = searchQuery ? 'flex' : 'none';
    }

    console.log('Söker efter:', searchQuery); // Debug

    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        // updateSummaryView anropas nu inifrån displayResults med filtrerade data
    }
}

function clearSearch() {
    if (searchInput) {
        searchInput.value = '';
    }
    searchQuery = '';
    if (clearSearchButton) {
        clearSearchButton.style.display = 'none';
    }

    console.log('Sökning rensad'); // Debug

    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        // updateSummaryView anropas nu inifrån displayResults med filtrerade data
    }
}

function sortProducts(products, sortOption) {
    return products.sort((a, b) => {
        switch (sortOption) {
            case 'stock_desc':
                const totalStockA = a.stocks.reduce((sum, s) => sum + s.stock, 0);
                const totalStockB = b.stocks.reduce((sum, s) => sum + s.stock, 0);
                return totalStockB - totalStockA;

            case 'stock_asc':
                const totalStockA2 = a.stocks.reduce((sum, s) => sum + s.stock, 0);
                const totalStockB2 = b.stocks.reduce((sum, s) => sum + s.stock, 0);
                return totalStockA2 - totalStockB2;

            case 'name_asc':
                return a.title.localeCompare(b.title, 'sv');

            case 'name_desc':
                return b.title.localeCompare(a.title, 'sv');

            case 'price_desc':
                const priceA = typeof a.price === 'number' ? a.price : 0;
                const priceB = typeof b.price === 'number' ? b.price : 0;
                return priceB - priceA;

            case 'price_asc':
                const priceA2 = typeof a.price === 'number' ? a.price : 0;
                const priceB2 = typeof b.price === 'number' ? b.price : 0;
                return priceA2 - priceB2;

            default:
                return 0;
        }
    });
}

function filterProductsBySearch(products, query) {
    if (!query || query.length < 1) return products;

    console.log(`Filtrerar ${products.length} produkter med query: "${query}"`); // Debug

    const filtered = products.filter(product => {
        const titleMatch = product.title && product.title.toLowerCase().includes(query);
        const eanMatch = product.ean && product.ean.toLowerCase().includes(query);
        const skuMatch = product.sku && product.sku.toLowerCase().includes(query);
        const productIdMatch = product.productId && product.productId.toLowerCase().includes(query);

        const matches = titleMatch || eanMatch || skuMatch || productIdMatch;

        if (matches) {
            console.log(`Match hittad: ${product.title}`); // Debug
        }

        return matches;
    });

    console.log(`Filtrering klar: ${filtered.length} produkter matchade`); // Debug
    return filtered;
}

async function loadStockData() {
    console.log(`--- loadStockData (Kat: ${currentSelectedCategory || 'Alla'}, Filter: ${currentStoreFilterMode}, Butiker: ${selectedStores.join(',')}) ---`);
    if(loadingDiv)loadingDiv.style.display='block'; if(errorDiv)errorDiv.style.display='none'; if(resultsDiv)resultsDiv.innerHTML='';
    if(ALL_PRODUCTS_FROM_API.length===0){console.warn("ALL_PRODUCTS_FROM_API är tom.");if(errorDiv){errorDiv.textContent="Produktkatalog ej laddad.";errorDiv.style.display='block';}if(loadingDiv)loadingDiv.style.display='none';return;}
    if(selectedStores.length===0){console.warn(`Inga valda butiker för: ${currentStoreFilterMode}`);if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga butiker.</h3><p>Välj filter/butiker.</p></div>`;if(loadingDiv)loadingDiv.style.display='none';updateLoadButton();return;}

    const storeIdsParam=selectedStores.join(',');
    const apiUrl=`${API_BASE_URL}/api/current_stock`;
    let qParams=[];
    if(storeIdsParam)qParams.push(`store_ids=${storeIdsParam}`);
    if(currentSelectedCategory)qParams.push(`category=${encodeURIComponent(currentSelectedCategory)}`);
    const fullApiUrl=`${apiUrl}${qParams.length>0?'?':''}${qParams.join('&')}`;console.log("Anropar API:",fullApiUrl);

    try{
        const response=await fetch(fullApiUrl);
        if(!response.ok){const errTxt=await response.text();throw new Error(`API: ${response.status}. ${errTxt.substring(0,100)}`);}
        const apiData=await response.json();

        // Debug: Kontrollera om tidsstämplar finns
        console.log('API Data sample (första 3 objekt):', apiData.slice(0, 3));
        const timestampSample = apiData.find(item => item.timestamp);
        if (timestampSample) {
            console.log('Hittade tidsstämpel i API-data:', timestampSample.timestamp);
        } else {
            console.warn('Ingen tidsstämpel hittades i API-data!');
        }

        displayedProductsCache=[];
        const productMap=new Map();
        const prodsToConsider=currentSelectedCategory?ALL_PRODUCTS_FROM_API.filter(p=>p.category===currentSelectedCategory):ALL_PRODUCTS_FROM_API;

        // Extrahera tidsstämplar för att hitta senaste uppdatering
        let latestTimestamp = null;

        prodsToConsider.forEach(pS=>{productMap.set(pS.productId.toString(),{productId:pS.productId.toString(),title:pS.title,category:pS.category,price:pS.price,ean:pS.ean,sku:pS.sku,stocks:[]});});
        apiData.forEach(item=>{
            const pIdStr=item.productId.toString();
            if(productMap.has(pIdStr)){
                const sD=stores.find(s=>s.storeId.toString()===item.storeId.toString());
                const sN=sD?sD.namn:`Butik ${item.storeId}`;
                const sC=sD?sD.stad:'';
                productMap.get(pIdStr).stocks.push({store:{namn:sN,stad:sC,storeId:item.storeId},stock:item.stockCount});

                // Spåra senaste tidsstämpel
                if(item.timestamp) {
                    const itemTimestamp = new Date(item.timestamp);
                    if(!latestTimestamp || itemTimestamp > latestTimestamp) {
                        latestTimestamp = itemTimestamp;
                    }
                }
            }
        });
        displayedProductsCache=Array.from(productMap.values());

        // Uppdatera tidsstämpel-visning
        updateLastUpdateInfo(latestTimestamp);

        displayResults(displayedProductsCache);
    }catch(err){
        console.error("Fel i loadStockData:",err);let msg=err.message;if(err instanceof SyntaxError)msg="Svar från servern felaktig JSON.";
        if(errorDiv){errorDiv.textContent=`Kunde ej hämta: ${msg}`;errorDiv.style.display='block';}
        if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Fel</h3><p>${msg}</p></div>`;
    }finally{if(loadingDiv)loadingDiv.style.display='none';}
}

function displayResults(productsToDisplayInput) {
    if(!resultsDiv) return; resultsDiv.innerHTML = ''; let productsActuallyShown = 0;
    let productsToRender = JSON.parse(JSON.stringify(productsToDisplayInput));

    // Filtrera baserat på sökfråga
    productsToRender = filterProductsBySearch(productsToRender, searchQuery);

    // Lägg till subGroup för kategorisering
    productsToRender.forEach(p => { p.subGroup = getProductSubGroup(p.title, p.category); });

    // Gruppera produkter per kategori och subgrupp
    const productsByCategory = {};
    productsToRender.forEach(product => {
        const category = product.category || "Okategoriserad";
        const subGroup = product.subGroup || "Övriga";

        if (!productsByCategory[category]) {
            productsByCategory[category] = {};
        }
        if (!productsByCategory[category][subGroup]) {
            productsByCategory[category][subGroup] = [];
        }
        productsByCategory[category][subGroup].push(product);
    });

    // Sortera kategorier
    const desiredCategoryOrder = ["Mac", "iPad", "iPhone", "Watch", "AirPods", "ATV & HomePods", "Tillbehör"];
    const sortedCategories = Object.keys(productsByCategory).sort((a, b) => {
        const indexA = desiredCategoryOrder.indexOf(a);
        const indexB = desiredCategoryOrder.indexOf(b);
        if (indexA !== -1 && indexB !== -1) return indexA - indexB;
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;
        return a.localeCompare(b);
    });

    // Rendera produkter kategori för kategori
    sortedCategories.forEach(categoryName => {
        const subGroups = productsByCategory[categoryName];

        // Lägg till kategoriheader
        const catH2 = document.createElement('h2');
        catH2.textContent = categoryName;
        catH2.style.cssText = "grid-column:1/-1;color:white;margin-top:30px;margin-bottom:15px;text-align:left;padding-bottom:5px;border-bottom:2px solid rgba(255,255,255,0.5);font-size:1.6rem;";
        resultsDiv.appendChild(catH2);

        // Sortera subgrupper
        const sortedSubGroups = Object.keys(subGroups).sort();

        sortedSubGroups.forEach(subGroupName => {
            let productsInSubGroup = subGroups[subGroupName];

            // Sortera produkter inom subgruppen
            productsInSubGroup = sortProducts(productsInSubGroup, currentSortOption);

            // Lägg till subgrupp-header om det inte är "Övriga"
            if (subGroupName !== "Övriga") {
                const subH3 = document.createElement('h3');
                subH3.textContent = subGroupName;
                subH3.style.cssText = "grid-column:1/-1;color:rgba(255,255,255,0.85);margin-top:15px;margin-bottom:8px;text-align:left;padding-left:10px;font-size:1.2rem;font-weight:500;";
                resultsDiv.appendChild(subH3);
            }

            // Rendera produkter i subgruppen
            productsInSubGroup.forEach(product => {
                const hasStock = product.stocks.some(s => s.stock > 0);
                let showThisCard = false;
                if (stockStatusFilter === 'in_stock' && hasStock) showThisCard = true;
                if (stockStatusFilter === 'out_of_stock' && !hasStock) showThisCard = true;
                if (stockStatusFilter === 'all') showThisCard = true;

                if (showThisCard) {
                    const pCard = document.createElement('div');
                    pCard.className = 'product-card';
                    if (!hasStock) pCard.classList.add('out-of-stock-card');

                    const sItemsHTML = product.stocks.map(s => {
                        let stockItemClass = 'stock-item';
                        let stockCountClass = 'stock-count';

                        if (s.stock === 0) {
                            stockItemClass += ' out-of-stock-item';
                            stockCountClass += ' empty';
                        } else if (s.stock === 1) {
                            stockItemClass += ' low-stock-item';
                            stockCountClass += ' low-stock';
                        }

                        return `<div class="${stockItemClass}">
                            <div class="store-info"><div class="store-name-small">${s.store.namn}</div><div class="store-city">${s.store.stad}</div></div>
                            <div class="${stockCountClass}">${s.stock} st</div>
                        </div>`;
                    }).join('');

                    let detailsHTML = `<div class="product-details">Art.nr: ${product.productId}`;
                    if (product.ean && currentDisplayMode === 'advanced') detailsHTML += `    EAN: ${product.ean}`;
                    if (product.sku && currentDisplayMode === 'advanced') detailsHTML += `<br>SKU: ${product.sku}`;
                    if (typeof product.price === 'number' && product.price > 0 && currentDisplayMode === 'advanced') {
                        detailsHTML += `<br>Pris: ${product.price.toLocaleString('sv-SE', { style: 'currency', currency: 'SEK' })}`;
                    }
                    detailsHTML += `</div>`;

                    pCard.innerHTML = `<div class="product-title">${product.title}</div> ${detailsHTML} <div class="stock-info">${sItemsHTML.length > 0 ? sItemsHTML : '<p style="font-style:italic;color:#777;text-align:center;">Inget lager i valda butiker.</p>'}</div>`;

                    // Gör produktkortet klickbart för att öppna produktanalys
                    pCard.style.cursor = 'pointer';
                    pCard.addEventListener('click', () => {
                        openProductAnalysis(product.productId);
                    });

                    resultsDiv.appendChild(pCard);
                    productsActuallyShown++;
                }
            });
        });
    });

    if (productsActuallyShown === 0) {
        const noResultsMsg = searchQuery ?
            `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga produkter</h3><p>Inga produkter matchade sökningen "${searchQuery}" och dina filter.</p></div>` :
            `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga produkter</h3><p>Inga produkter matchade dina filter.</p></div>`;
        resultsDiv.innerHTML = noResultsMsg;
    } else {
        updateSummaryView(productsToRender); // Använd filtrerade listan för att reflektera aktuell sökning
    }
}

function updateSummaryView(productsToSummarize) {
    if (!summarySectionDiv || !summaryContentDiv) { console.error("Summerings-DOM-element saknas!"); return; }
    if (!productsToSummarize || productsToSummarize.length === 0) {
        summarySectionDiv.style.display = 'none'; return;
    }
    let totalUnitsInStock = 0; let totalStockValue = 0; const uniqueProductIdsWithStock = new Set(); const stockPerStore = {};
    productsToSummarize.forEach(product => {
        const price = typeof product.price === 'number' ? product.price : null;
        product.stocks.forEach(stockItem => {
            if (stockItem.stock > 0) {
                totalUnitsInStock += stockItem.stock; uniqueProductIdsWithStock.add(product.productId);
                if (price !== null) totalStockValue += stockItem.stock * price;
                const storeId = stockItem.store.storeId.toString();
                if (!stockPerStore[storeId]) stockPerStore[storeId] = { name: stockItem.store.namn, city: stockItem.store.stad, units: 0, value: 0 };
                stockPerStore[storeId].units += stockItem.stock;
                if (price !== null) stockPerStore[storeId].value += stockItem.stock * price;
            }
        });
    });
    const productsWithStockCount = uniqueProductIdsWithStock.size;
    let summaryHTML = `<dl class="summary-details-list">`;
    summaryHTML += `<dt>Totalt enheter i lager:</dt><dd>${totalUnitsInStock} st</dd>`;
    summaryHTML += `<dt>Antal unika produkter m. lager:</dt><dd>${productsWithStockCount}</dd>`;
    if (currentDisplayMode === 'advanced') {
        if (totalStockValue > 0) summaryHTML += `<dt>Uppskattat lagervärde:</dt><dd>${totalStockValue.toLocaleString('sv-SE',{style:'currency',currency:'SEK'})}</dd>`;
        summaryHTML += `</dl>`;
        if (Object.keys(stockPerStore).length > 1 && totalUnitsInStock > 0) {
            summaryHTML += `<h4 style="margin-top:15px;font-size:1em;">Lagerfördelning per butik:</h4>`;
            summaryHTML += `<table style="font-size:0.9em;width:100%;"><thead><tr><th>Butik</th><th>Enheter</th><th>Andel</th><th>Värde</th></tr></thead><tbody>`;
            const sortedStoresByUnits=Object.entries(stockPerStore).sort(([,a],[,b])=>b.units-a.units);
            for(const[,storeData]of sortedStoresByUnits){const pcent=totalUnitsInStock>0?((storeData.units/totalUnitsInStock)*100).toFixed(1):"0.0";summaryHTML+=`<tr><td>${storeData.name} (${storeData.city})</td><td>${storeData.units} st</td><td>${pcent}%</td><td>${storeData.value > 0 ? storeData.value.toLocaleString('sv-SE',{style:'currency',currency:'SEK'}):'-'}</td></tr>`;}
            summaryHTML+=`</tbody></table>`;
        } else if (Object.keys(stockPerStore).length===1&&totalUnitsInStock>0){const sId=Object.keys(stockPerStore)[0];const sData=stockPerStore[sId];if(sData.value>0)summaryHTML+=`<p style="margin-top:10px;"><strong>Lagervärde (${sData.name}):</strong> ${sData.value.toLocaleString('sv-SE',{style:'currency',currency:'SEK'})}</p>`;}
    } else { summaryHTML += `</dl>`; if(Object.keys(stockPerStore).length>0&&totalUnitsInStock>0){summaryHTML+=`<p style="font-size:0.9em;margin-top:10px;">`;let sParts=[];for(const sId in stockPerStore){if(stockPerStore[sId].units>0)sParts.push(`${stockPerStore[sId].name}: ${stockPerStore[sId].units}st`);}summaryHTML+=sParts.join(' | ');summaryHTML+=`</p>`;}}
    summaryContentDiv.innerHTML = summaryHTML; summarySectionDiv.style.display = 'block';
}

function updateLastUpdateInfo(timestamp) {
    const lastUpdateDiv = document.getElementById('last-update-info');
    if (!lastUpdateDiv) return;

    if (!timestamp) {
        lastUpdateDiv.style.display = 'none';
        return;
    }

    // Formatera tidsstämpeln på svenska
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Europe/Stockholm'
    };

    const formattedTime = timestamp.toLocaleDateString('sv-SE', options);

    // Beräkna hur länge sedan uppdateringen var
    const now = new Date();
    const diffMs = now - timestamp;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    let timeAgo = '';
    if (diffMinutes < 1) {
        timeAgo = 'mindre än en minut sedan';
    } else if (diffMinutes < 60) {
        timeAgo = `${diffMinutes} minut${diffMinutes !== 1 ? 'er' : ''} sedan`;
    } else if (diffHours < 24) {
        timeAgo = `${diffHours} timm${diffHours !== 1 ? 'ar' : 'e'} sedan`;
    } else if (diffDays < 7) {
        timeAgo = `${diffDays} dag${diffDays !== 1 ? 'ar' : ''} sedan`;
    } else {
        timeAgo = 'mer än en vecka sedan';
    }

    lastUpdateDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="margin-bottom: 3px;">📊 Senaste lageruppdatering:</div>
            <div style="font-weight: 500;">${formattedTime}</div>
            <div style="font-size: 0.75rem; opacity: 0.8;">(${timeAgo})</div>
        </div>
    `;
    lastUpdateDiv.style.display = 'block';
}

document.addEventListener('DOMContentLoaded', () => {
    if (viewModeToggleButton) {
        viewModeToggleButton.onclick = () => toggleDisplayMode();
        // Sätt initialt tillstånd för avancerad vy
        viewModeToggleButton.textContent = 'Kompakt vy';
        viewModeToggleButton.classList.add('active');
    }

    // Sökfunktionalitet
    if (searchInput) {
        // Realtidssökning med debounce
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            console.log('Sökinput ändrad:', e.target.value); // Debug
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 200); // 200ms debounce för snabbare respons
        });

        // Sök även när användaren trycker Enter
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                clearTimeout(searchTimeout);
                performSearch();
            }
        });

        // Fokusera sökrutan när användaren trycker Ctrl+F eller Cmd+F
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                searchInput.focus();
            }
        });
    }

    // Sätt initial sortering
    if (sortDropdown) {
        sortDropdown.value = currentSortOption;
    }

    initializeApp();
});

// Funktion för att öppna produktanalys
function openProductAnalysis(productId) {
    // Skicka med valda butiker från index-sidan
    const selectedStoreIds = selectedStores.join(',');
    const url = `product.html?productId=${productId}&stores=${selectedStoreIds}`;
    window.open(url, '_blank');
}
