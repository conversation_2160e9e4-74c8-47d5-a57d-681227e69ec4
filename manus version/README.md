# Power Lagerkollen - Användarguide

## Översikt
Power Lagerkollen är en webbapplikation som hjälper Power-anställda att snabbt se vilka Apple-produkter som finns i lager i sin butik och närliggande butiker. Applikationen hämtar realtidsdata från Power's API och visar endast produkter som finns tillgängliga.

## Funktioner
- Visar Apple-produkter som finns i lager i din valda butik och närliggande butiker
- Automatisk identifiering av närmaste butiker baserat på din position
- Filtrering av produkter efter kategori (iPhone, Mac, AirPods, etc.)
- Tydlig indikation på vilka produkter som finns i vilka butiker och hur många som finns i lager
- Direktlänkar till produktsidor på Power's webbplats

## Teknisk lösning
Applikationen är byggd med följande teknologier:
- HTML5, CSS3 och JavaScript (vanilla, ingen ramverk)
- Använder Power's öppna API för lagerstatus
- Dynamisk inläsning av produkter från plist-filer
- Responsiv design som fungerar på både desktop och mobila enheter

## Användning

### Starta applikationen
1. Öppna den tillhandahållna länken i din webbläsare
2. Applikationen kommer automatiskt att försöka hitta din position för att visa närmaste butiker
3. Om positionering inte fungerar, visas standardbutiker (Power Bäckebol, Högsbo och Torpavallen)

### Välja butiker
1. Butiker visas i en lista, sorterade efter avstånd om positionering fungerar
2. Din närmaste butik väljs automatiskt som primärbutik (markerad)
3. Du kan klicka på andra butiker för att lägga till eller ta bort dem från urvalet
4. Minst en butik måste vara vald för att kunna ladda lagerstatus

### Filtrera produkter
1. Använd rullgardinsmenyn "Filtrera efter kategori" för att välja vilken typ av Apple-produkter du vill se
2. Välj "Alla kategorier" för att se produkter från samtliga kategorier

### Ladda lagerstatus
1. Klicka på "Ladda lagerstatus"-knappen för att hämta aktuell lagerstatus för valda butiker
2. En framstegsindikator visar hur långt processen har kommit
3. När processen är klar visas alla produkter som finns i lager i de valda butikerna

### Produktvisning
- Produkter grupperas efter kategori (iPhone, Mac, etc.)
- Varje produkt visas med namn och lagerstatus för varje butik där den finns i lager
- Primärbutiken markeras särskilt i listan
- Klicka på "Visa produkt" för att gå direkt till produktsidan på Power's webbplats

## Felsökning
Om applikationen inte visar några produkter, kontrollera följande:
1. Säkerställ att du har en aktiv internetanslutning
2. Kontrollera att minst en butik är vald
3. Prova att välja en annan kategori eller "Alla kategorier"
4. Om du får ett CORS-felmeddelande, beror det på webbläsarens säkerhetsbegränsningar. I en produktionsmiljö skulle detta lösas med en server-proxy eller CORS-headers från Power.

## Tekniska detaljer
- Applikationen använder API-endpointen `https://www.power.se/api/sparoute/datorer-och-surfplattor/p-[produktid]/` för att hämta lagerstatus
- Produktdata laddas dynamiskt från plist-filer
- Butiksinformation med koordinater används för att beräkna närmaste butiker
- Endast produkter med lagervärde > 0 visas i resultatlistan
