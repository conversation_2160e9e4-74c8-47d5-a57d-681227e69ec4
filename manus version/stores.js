// stores.js - Hantering av butiker och närliggande butiker

// Butiker från JSON-filen
const stores = [
    {"storeId": 7152, "namn": "POWER Torpavallen", "adress": "Torpavallsgatan 4D", "postnummer": "41673", "stad": "Göteborg", "region": "Västra Götaland", "lat": 57.6792, "lng": 11.9894},
    {"storeId": 7150, "namn": "POWER Högsbo", "adress": "Lona Knapes gata 1", "postnummer": "42132", "stad": "Västra Frölunda", "region": "Västra Götaland", "lat": 57.6348, "lng": 11.9059},
    {"storeId": 7151, "namn": "POWER Bäckebol", "adress": "Transportgatan 19", "postnummer": "42246", "stad": "Hisings Backa", "region": "Västra Götaland", "lat": 57.7461, "lng": 11.9094},
    {"storeId": 7156, "namn": "POW<PERSON> Borås", "adress": "Ålgårdsvägen 11", "postnummer": "50630", "stad": "Borås", "region": "Västra Götaland", "lat": 57.7210, "lng": 12.9401},
    {"storeId": 7125, "namn": "POWER Jönköping", "adress": "Solåsvägen 4A", "postnummer": "55303", "stad": "Jönköping", "region": "Jönköping", "lat": 57.7826, "lng": 14.1618},
    {"storeId": 7155, "namn": "POWER Skövde", "adress": "Jonstorpsgatan 3C", "postnummer": "54937", "stad": "Skövde", "region": "Västra Götaland", "lat": 58.3875, "lng": 13.8458},
    {"storeId": 7147, "namn": "POWER Helsingborg Väla", "adress": "Marknadsvägen 5", "postnummer": "25469", "stad": "Ödåkra", "region": "Skåne", "lat": 56.0776, "lng": 12.7441},
    {"storeId": 7170, "namn": "POWER Västerås", "adress": "Hallsta Gårdsgata 7", "postnummer": "72138", "stad": "Västerås", "region": "Västmanland", "lat": 59.6099, "lng": 16.5448},
    {"storeId": 7148, "namn": "POWER Lund", "adress": "Avtalsvägen 2", "postnummer": "22761", "stad": "Lund", "region": "Skåne", "lat": 55.7047, "lng": 13.2900},
    {"storeId": 7149, "namn": "POWER Kristianstad", "adress": "Fundamentgatan 1", "postnummer": "29161", "stad": "Kristianstad", "region": "Skåne", "lat": 56.0280, "lng": 14.1567},
    {"storeId": 7153, "namn": "POWER Linköping", "adress": "Björkgatan 4", "postnummer": "58252", "stad": "Linköping", "region": "Östergötland", "lat": 58.4108, "lng": 15.6214},
    {"storeId": 7154, "namn": "POWER Norrköping", "adress": "Koppargatan 30", "postnummer": "60223", "stad": "Norrköping", "region": "Östergötland", "lat": 58.5877, "lng": 16.1924},
    {"storeId": 7146, "namn": "POWER Malmö Svågertorp", "adress": "Nornegatan 8", "postnummer": "21586", "stad": "Malmö", "region": "Skåne", "lat": 55.5636, "lng": 12.9719},
    {"storeId": 7157, "namn": "POWER Uppsala", "adress": "Stångjärnsgatan 10", "postnummer": "75323", "stad": "Uppsala", "region": "Uppsala", "lat": 59.8586, "lng": 17.6389},
    {"storeId": 7158, "namn": "POWER Örebro", "adress": "Bettorpsgatan 4", "postnummer": "70369", "stad": "Örebro", "region": "Örebro", "lat": 59.2741, "lng": 15.2066},
    {"storeId": 7159, "namn": "POWER Sundsvall", "adress": "Norra Förmansvägen 18", "postnummer": "86341", "stad": "Sundsvall", "region": "Västernorrland", "lat": 62.3908, "lng": 17.3069},
    {"storeId": 7160, "namn": "POWER Gävle", "adress": "Ingenjörsgatan 2", "postnummer": "80293", "stad": "Gävle", "region": "Gävleborg", "lat": 60.6749, "lng": 17.1413},
    {"storeId": 7161, "namn": "POWER Stockholm Kungens Kurva", "adress": "Geometrivägen 1", "postnummer": "14175", "stad": "Kungens Kurva", "region": "Stockholm", "lat": 59.2465, "lng": 17.9414},
    {"storeId": 7162, "namn": "POWER Stockholm Barkarby", "adress": "Herrestavägen 20", "postnummer": "17738", "stad": "Järfälla", "region": "Stockholm", "lat": 59.4142, "lng": 17.8907}
];

// Standardbutiker om geolokalisering inte fungerar
const DEFAULT_PRIMARY_STORE_ID = 7151; // Power Bäckebol
const DEFAULT_NEARBY_STORE_IDS = [7150, 7152]; // Power Högsbo och Power Torpavallen

let userLocation = null;
let selectedStores = [];

// Beräkna avstånd mellan två koordinater med Haversine-formeln
function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // Jordens radie i km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}

// Hämta användarens position
function getUserLocation() {
    return new Promise((resolve) => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    userLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    document.getElementById('location-status').textContent = 
                        `Position hittad - visar närliggande butiker`;
                    
                    const nearestStores = findNearestStores(userLocation, 3);
                    resolve(nearestStores);
                },
                (error) => {
                    console.log('Geolocation error:', error);
                    document.getElementById('location-status').textContent = 
                        'Kunde inte hitta position - visar standardbutiker';
                    
                    // Använd standardbutiker om geolokalisering misslyckas
                    const defaultStores = {
                        primary: stores.find(store => store.storeId === DEFAULT_PRIMARY_STORE_ID),
                        nearby: stores.filter(store => DEFAULT_NEARBY_STORE_IDS.includes(store.storeId))
                    };
                    
                    resolve(defaultStores);
                }
            );
        } else {
            document.getElementById('location-status').textContent = 
                'Geolocation stöds inte - visar standardbutiker';
            
            // Använd standardbutiker om geolokalisering inte stöds
            const defaultStores = {
                primary: stores.find(store => store.storeId === DEFAULT_PRIMARY_STORE_ID),
                nearby: stores.filter(store => DEFAULT_NEARBY_STORE_IDS.includes(store.storeId))
            };
            
            resolve(defaultStores);
        }
    });
}

// Hitta närmaste butiker baserat på användarens position
function findNearestStores(location, count = 3) {
    if (!location) {
        return {
            primary: stores.find(store => store.storeId === DEFAULT_PRIMARY_STORE_ID),
            nearby: stores.filter(store => DEFAULT_NEARBY_STORE_IDS.includes(store.storeId))
        };
    }
    
    // Beräkna avstånd till varje butik
    const storesWithDistance = stores.map(store => ({
        ...store,
        distance: calculateDistance(location.lat, location.lng, store.lat, store.lng)
    }));
    
    // Sortera butiker efter avstånd
    storesWithDistance.sort((a, b) => a.distance - b.distance);
    
    // Returnera närmaste butik som primär och de näst närmaste som närliggande
    return {
        primary: storesWithDistance[0],
        nearby: storesWithDistance.slice(1, count)
    };
}

// Visa butiker sorterade efter avstånd
function displayStores(storeSelector, primaryStore, nearbyStores) {
    storeSelector.innerHTML = '';
    
    // Lägg till primärbutiken först
    if (primaryStore) {
        const storeCard = createStoreCard(primaryStore, true);
        storeSelector.appendChild(storeCard);
        
        // Lägg till primärbutiken i selectedStores
        if (!selectedStores.includes(primaryStore.storeId)) {
            selectedStores = [primaryStore.storeId];
        }
    }
    
    // Lägg till närliggande butiker
    if (nearbyStores && nearbyStores.length > 0) {
        nearbyStores.forEach(store => {
            const storeCard = createStoreCard(store, selectedStores.includes(store.storeId));
            storeSelector.appendChild(storeCard);
            
            // Lägg till närliggande butiker i selectedStores
            if (!selectedStores.includes(store.storeId)) {
                selectedStores.push(store.storeId);
            }
        });
    }
    
    // Lägg till resterande butiker
    const otherStores = stores.filter(store => 
        store.storeId !== primaryStore?.storeId && 
        !nearbyStores.some(nearby => nearby.storeId === store.storeId)
    );
    
    otherStores.forEach(store => {
        const storeCard = createStoreCard(store, selectedStores.includes(store.storeId));
        storeSelector.appendChild(storeCard);
    });
    
    updateLoadButton();
}

// Skapa ett butikskort
function createStoreCard(store, isSelected) {
    const storeCard = document.createElement('div');
    storeCard.className = `store-card ${isSelected ? 'selected' : ''}`;
    storeCard.onclick = () => toggleStore(store.storeId);
    
    let distanceText = '';
    if (store.distance) {
        distanceText = `<div class="distance">${store.distance.toFixed(1)} km bort</div>`;
    }
    
    storeCard.innerHTML = `
        <div class="store-name">${store.namn}</div>
        <div class="store-address">${store.adress}, ${store.stad}</div>
        ${distanceText}
    `;
    
    return storeCard;
}

// Växla val av butik
function toggleStore(storeId) {
    const index = selectedStores.indexOf(storeId);
    if (index > -1) {
        selectedStores.splice(index, 1);
    } else {
        selectedStores.push(storeId);
    }
    
    // Uppdatera butikskortet
    const storeCards = document.querySelectorAll('.store-card');
    storeCards.forEach(card => {
        const cardStoreId = parseInt(card.getAttribute('data-store-id'));
        if (cardStoreId === storeId) {
            card.classList.toggle('selected');
        }
    });
    
    updateLoadButton();
}

// Uppdatera load-knappen
function updateLoadButton() {
    const button = document.getElementById('load-button');
    button.disabled = selectedStores.length === 0;
    button.textContent = selectedStores.length === 0 ? 
        'Välj minst en butik' : 
        `Ladda lagerstatus (${selectedStores.length} butiker)`;
}

// Exportera funktioner och variabler
export { 
    stores, 
    getUserLocation, 
    findNearestStores, 
    displayStores, 
    toggleStore, 
    updateLoadButton, 
    selectedStores 
};
