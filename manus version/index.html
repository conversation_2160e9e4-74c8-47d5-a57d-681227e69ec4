<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power Lagerkollen</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #e60000 0%, #990000 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .location-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .location-icon {
            width: 40px;
            height: 40px;
            background: #e60000;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .filter-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .filter-control {
            flex: 1;
        }

        .filter-control label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .filter-control select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }

        .store-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .store-card {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .store-card:hover {
            border-color: #e60000;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .store-card.selected {
            border-color: #e60000;
            background: #fff8f8;
        }

        .store-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .store-address {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .distance {
            color: #e60000;
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 5px;
        }

        .load-button {
            background: linear-gradient(135deg, #e60000 0%, #990000 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            width: 100%;
        }

        .load-button:hover {
            transform: translateY(-2px);
        }

        .load-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            text-align: center;
            padding: 30px;
            color: white;
            font-size: 1.2rem;
            background: rgba(0,0,0,0.2);
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .progress-container {
            width: 100%;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-bar {
            height: 20px;
            background: linear-gradient(135deg, #e60000 0%, #990000 100%);
            width: 0%;
            transition: width 0.3s ease;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .category-header {
            background: #e60000;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            margin: 20px 0 15px;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .results {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            padding: 20px;
            line-height: 1.4;
            border-bottom: 1px solid #eee;
        }

        .stock-info {
            display: grid;
            gap: 10px;
            padding: 15px;
        }

        .stock-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stock-item.primary-store {
            background: #fff8f8;
            border-left: 3px solid #e60000;
        }

        .store-info {
            flex: 1;
        }

        .store-name-small {
            font-weight: 500;
            color: #2c3e50;
        }

        .store-city {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .stock-count {
            background: #28a745;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .product-link {
            display: block;
            background: #e60000;
            color: white;
            text-align: center;
            padding: 15px;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s ease;
        }

        .product-link:hover {
            background: #990000;
        }

        .error {
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .filter-controls {
                flex-direction: column;
            }
            
            .store-selector {
                grid-template-columns: 1fr;
            }
            
            .results {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Power Lagerkollen</h1>
            <p>Intern portal för lagerkoll av Apple-produkter</p>
        </div>

        <div class="controls">
            <div class="location-info">
                <div class="location-icon">📍</div>
                <div>
                    <div id="location-status">Söker din position...</div>
                    <div style="font-size: 0.9rem; color: #6c757d;">Välj dina butiker nedan</div>
                </div>
            </div>

            <div class="filter-controls">
                <div class="filter-control">
                    <label for="category-filter">Filtrera efter kategori</label>
                    <select id="category-filter">
                        <option value="all">Alla kategorier</option>
                        <!-- Kategorier läggs till dynamiskt -->
                    </select>
                </div>
            </div>

            <div class="store-selector" id="store-selector">
                <!-- Butiker läggs till här dynamiskt -->
            </div>

            <button class="load-button" id="load-button">
                Ladda lagerstatus
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <div id="loading-message">Hämtar lagerstatus...</div>
            <div class="progress-container">
                <div class="progress-bar" id="progress-bar">
                    <span id="progress-text">0%</span>
                </div>
            </div>
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="results" class="results"></div>
    </div>

    <script type="module" src="app.js"></script>
</body>
</html>
