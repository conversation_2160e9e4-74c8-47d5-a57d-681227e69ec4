// inventory.js - Hantering av lagerstatus för produkter

// Funktion för att hämta lagerstatus för en produkt
async function fetchProductInventory(productId) {
    try {
        // Använder den korrekta API-endpointen baserat på användarens feedback
        // Notera att vi använder /x/p- istället för /datorer-och-surfplattor/p-
        const response = await fetch(`https://www.power.se/api/sparoute/x/p-${productId}/`);
        
        if (!response.ok) {
            throw new Error(`API-anrop misslyckades: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Kontrollera att vi har fått rätt data
        if (data && data.Model && data.Model.ExtendedProduct && data.Model.ExtendedProduct.ProductStoreStocks) {
            return data.Model.ExtendedProduct.ProductStoreStocks;
        } else {
            console.error(`Ogiltig datastruktur för produkt ${productId}`);
            return null;
        }
    } catch (error) {
        console.error(`Fel vid hämtning av lagerstatus för produkt ${productId}:`, error);
        
        // Om vi får CORS-fel, visa en tydlig felmeddelande
        if (error.message.includes('CORS') || error.message.includes('Cross-Origin')) {
            document.getElementById('error').textContent = 
                'CORS-fel: Kan inte hämta data direkt från Power API. Detta är en begränsning i webbläsaren. ' +
                'För en produktionslösning behöver du en server-proxy eller CORS-headers från Power.';
            document.getElementById('error').style.display = 'block';
        }
        
        return null;
    }
}

// Funktion för att hämta lagerstatus för flera produkter
async function fetchMultipleProductInventory(products, updateProgress) {
    const inventoryMap = {};
    let completedCount = 0;
    
    // Skapa en kö av produkter för att undvika att överbelasta API:et
    const productQueue = [...products];
    const batchSize = 5; // Antal parallella anrop
    
    while (productQueue.length > 0) {
        // Ta ut nästa batch av produkter
        const batch = productQueue.splice(0, batchSize);
        
        // Skapa en array av promises för denna batch
        const batchPromises = batch.map(product => 
            fetchProductInventory(product.productId)
                .then(inventory => {
                    inventoryMap[product.productId] = inventory;
                    completedCount++;
                    
                    // Uppdatera framsteg om callback finns
                    if (updateProgress) {
                        const progress = Math.round((completedCount / products.length) * 100);
                        updateProgress(progress);
                    }
                })
                .catch(error => {
                    console.error(`Fel för produkt ${product.productId}:`, error);
                    inventoryMap[product.productId] = null;
                    completedCount++;
                    
                    // Uppdatera framsteg även vid fel
                    if (updateProgress) {
                        const progress = Math.round((completedCount / products.length) * 100);
                        updateProgress(progress);
                    }
                })
        );
        
        // Vänta på att alla anrop i denna batch är klara
        await Promise.all(batchPromises);
        
        // Lägg in en liten paus mellan batches för att inte överbelasta API:et
        if (productQueue.length > 0) {
            await new Promise(resolve => setTimeout(resolve, 300));
        }
    }
    
    return inventoryMap;
}

// Funktion för att kontrollera om en produkt finns i lager i en specifik butik
function isProductInStock(inventoryData, storeId) {
    if (!inventoryData || !inventoryData[storeId] || !inventoryData[storeId].Stock) {
        return false;
    }
    
    // Kontrollera om lagervärdet är större än 0
    return inventoryData[storeId].Stock > 0;
}

// Funktion för att filtrera produkter baserat på lagerstatus i valda butiker
function filterProductsByAvailability(products, inventoryMap, selectedStoreIds) {
    return products.filter(product => {
        const inventory = inventoryMap[product.productId];
        
        // Kontrollera om produkten finns i någon av de valda butikerna
        return selectedStoreIds.some(storeId => 
            isProductInStock(inventory, storeId)
        );
    });
}

// Funktion för att hämta lagerstatus för alla produkter och filtrera baserat på tillgänglighet
async function getAvailableProducts(products, selectedStoreIds, updateProgress) {
    // Hämta lagerstatus för alla produkter
    const inventoryMap = await fetchMultipleProductInventory(products, updateProgress);
    
    // Filtrera produkter baserat på lagerstatus
    const availableProducts = filterProductsByAvailability(
        products, 
        inventoryMap, 
        selectedStoreIds
    );
    
    // Lägg till lagerstatus i produktobjekten
    return availableProducts.map(product => {
        const inventory = inventoryMap[product.productId];
        
        // Skapa en array med butiker där produkten finns i lager
        const stockInfo = selectedStoreIds
            .filter(storeId => isProductInStock(inventory, storeId))
            .map(storeId => ({
                storeId,
                stock: inventory[storeId].Stock
            }));
        
        return {
            ...product,
            stockInfo
        };
    });
}

// Exportera funktioner
export { 
    fetchProductInventory, 
    fetchMultipleProductInventory, 
    isProductInStock, 
    filterProductsByAvailability,
    getAvailableProducts 
};
