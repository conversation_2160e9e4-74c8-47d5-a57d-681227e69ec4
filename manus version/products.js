// products.js - Hantering av produkter från plist-filer

// Funk<PERSON> för att parsa plist-filer och extrahera produktinformation
async function parseProductList(plistUrl) {
    try {
        // Hämta plist-filen som text
        const response = await fetch(plistUrl);
        const plistText = await response.text();
        
        // Enkel parser för att extrahera produktinformation från plist
        const products = [];
        let currentProduct = {};
        
        // Dela upp plist-filen i rader
        const lines = plistText.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Starta ett nytt produktobjekt
            if (line === '<dict>') {
                currentProduct = {};
            }
            
            // Avsluta ett produktobjekt och lägg till det i listan
            else if (line === '</dict>') {
                if (Object.keys(currentProduct).length > 0) {
                    products.push(currentProduct);
                }
            }
            
            // Extrahera produktID
            else if (line === '<key>productId</key>') {
                const idLine = lines[i + 1].trim();
                if (idLine.startsWith('<integer>') && idLine.endsWith('</integer>')) {
                    currentProduct.productId = parseInt(idLine.substring(9, idLine.length - 10));
                }
                i++; // Hoppa över nästa rad eftersom vi redan har läst den
            }
            
            // Extrahera produkttitel
            else if (line === '<key>title</key>') {
                const titleLine = lines[i + 1].trim();
                if (titleLine.startsWith('<string>') && titleLine.endsWith('</string>')) {
                    currentProduct.title = titleLine.substring(8, titleLine.length - 9);
                }
                i++; // Hoppa över nästa rad eftersom vi redan har läst den
            }
            
            // Extrahera produkt-URL
            else if (line === '<key>url</key>') {
                const urlLine = lines[i + 1].trim();
                if (urlLine.startsWith('<string>') && urlLine.endsWith('</string>')) {
                    currentProduct.url = urlLine.substring(8, urlLine.length - 9);
                    // Lägg till fullständig URL
                    currentProduct.fullUrl = `https://www.power.se${urlLine.substring(8, urlLine.length - 9)}`;
                }
                i++; // Hoppa över nästa rad eftersom vi redan har läst den
            }
        }
        
        return products;
    } catch (error) {
        console.error('Fel vid parsning av produktlista:', error);
        return [];
    }
}

// Funktion för att ladda alla produkter från alla plist-filer
async function loadAllProducts() {
    const categories = [
        { name: 'AirPods', file: 'airpods.plist' },
        { name: 'Apple TV', file: 'appletv.plist' },
        { name: 'HomePod', file: 'homepod.plist' },
        { name: 'iPhone', file: 'iphone.plist' },
        { name: 'Mac', file: 'mac.plist' },
        { name: 'Tillbehör', file: 'tillbehor.plist' },
        { name: 'Watch', file: 'watch.plist' }
    ];
    
    const allProducts = [];
    
    for (const category of categories) {
        try {
            const products = await parseProductList(category.file);
            
            // Lägg till kategori till varje produkt
            products.forEach(product => {
                product.category = category.name;
            });
            
            allProducts.push(...products);
        } catch (error) {
            console.error(`Fel vid laddning av ${category.name} produkter:`, error);
        }
    }
    
    return allProducts;
}

// Exportera funktioner
export { parseProductList, loadAllProducts };
