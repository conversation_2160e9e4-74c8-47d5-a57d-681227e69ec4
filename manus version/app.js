// app.js - Huvudapplikation för Power Lagerkollen

import { stores, getUserLocation, displayStores, selectedStores } from './stores.js';
import { loadAllProducts } from './products.js';
import { getAvailableProducts } from './inventory.js';

// DOM-element som kommer att användas
let storeSelector;
let loadButton;
let loadingElement;
let errorElement;
let resultsElement;
let progressBar;
let progressText;
let locationStatus;
let categoryFilter;

// Globala variabler
let allProducts = [];
let primaryStore = null;
let nearbyStores = [];
let filteredProducts = [];

// Initialisera applikationen
async function initApp() {
    // Hämta DOM-element
    storeSelector = document.getElementById('store-selector');
    loadButton = document.getElementById('load-button');
    loadingElement = document.getElementById('loading');
    errorElement = document.getElementById('error');
    resultsElement = document.getElementById('results');
    progressBar = document.getElementById('progress-bar');
    progressText = document.getElementById('progress-text');
    locationStatus = document.getElementById('location-status');
    categoryFilter = document.getElementById('category-filter');
    
    try {
        // Visa laddningsindikator
        showLoading(true, 'Initierar applikation...');
        
        // Ladda alla produkter från plist-filer
        allProducts = await loadAllProducts();
        console.log(`Laddade ${allProducts.length} produkter`);
        
        // Populera kategorifilter
        populateCategoryFilter();
        
        // Hämta användarens position och närliggande butiker
        const storeInfo = await getUserLocation();
        primaryStore = storeInfo.primary;
        nearbyStores = storeInfo.nearby;
        
        // Visa butiker
        displayStores(storeSelector, primaryStore, nearbyStores);
        
        // Lägg till händelselyssnare för load-knappen
        loadButton.addEventListener('click', loadStockData);
        
        // Lägg till händelselyssnare för kategorifilter
        categoryFilter.addEventListener('change', filterProductsByCategory);
        
        // Dölj laddningsindikator
        showLoading(false);
    } catch (error) {
        console.error('Fel vid initialisering av applikationen:', error);
        showError('Ett fel uppstod vid initialisering av applikationen. Försök igen senare.');
        showLoading(false);
    }
}

// Populera kategorifilter
function populateCategoryFilter() {
    // Hämta unika kategorier
    const categories = [...new Set(allProducts.map(product => product.category))];
    
    // Rensa befintliga alternativ
    categoryFilter.innerHTML = '<option value="all">Alla kategorier</option>';
    
    // Lägg till kategorier
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
    });
}

// Filtrera produkter baserat på vald kategori
function filterProductsByCategory() {
    const selectedCategory = categoryFilter.value;
    
    if (selectedCategory === 'all') {
        // Visa alla produkter
        filteredProducts = [...allProducts];
    } else {
        // Filtrera produkter baserat på kategori
        filteredProducts = allProducts.filter(product => product.category === selectedCategory);
    }
    
    // Om vi redan har laddat lagerstatus, uppdatera resultaten
    if (document.querySelector('.product-card')) {
        loadStockData();
    }
}

// Ladda lagerstatus
async function loadStockData() {
    // Kontrollera att vi har valda butiker
    if (selectedStores.length === 0) {
        showError('Välj minst en butik för att ladda lagerstatus.');
        return;
    }
    
    // Kontrollera att vi har produkter att visa
    if (filteredProducts.length === 0) {
        filterProductsByCategory(); // Initialisera filteredProducts om det behövs
    }
    
    // Visa laddningsindikator
    showLoading(true, 'Hämtar lagerstatus...');
    resetProgress();
    
    try {
        // Hämta tillgängliga produkter
        const availableProducts = await getAvailableProducts(
            filteredProducts,
            selectedStores,
            updateProgress
        );
        
        // Visa resultaten
        displayResults(availableProducts);
    } catch (error) {
        console.error('Fel vid hämtning av lagerstatus:', error);
        showError('Ett fel uppstod vid hämtning av lagerstatus. Försök igen senare.');
    } finally {
        showLoading(false);
    }
}

// Uppdatera framstegsindikator
function updateProgress(progress) {
    progressBar.style.width = `${progress}%`;
    progressText.textContent = `${progress}%`;
}

// Återställ framstegsindikator
function resetProgress() {
    progressBar.style.width = '0%';
    progressText.textContent = '0%';
}

// Visa/dölj laddningsindikator
function showLoading(show, message = 'Laddar...') {
    loadingElement.style.display = show ? 'block' : 'none';
    document.getElementById('loading-message').textContent = message;
}

// Visa felmeddelande
function showError(message) {
    errorElement.textContent = message;
    errorElement.style.display = 'block';
    
    // Dölj felmeddelandet efter 5 sekunder
    setTimeout(() => {
        errorElement.style.display = 'none';
    }, 5000);
}

// Visa resultat
function displayResults(products) {
    resultsElement.innerHTML = '';
    
    if (products.length === 0) {
        resultsElement.innerHTML = `
            <div style="grid-column: 1/-1; text-align: center; color: white; padding: 50px;">
                <h3>Inga produkter i lager</h3>
                <p>Inga av de valda butikerna har produkter i lager just nu för den valda kategorin.</p>
            </div>
        `;
        return;
    }
    
    // Gruppera produkter efter kategori
    const productsByCategory = {};
    
    products.forEach(product => {
        const category = product.category || 'Övrigt';
        
        if (!productsByCategory[category]) {
            productsByCategory[category] = [];
        }
        
        productsByCategory[category].push(product);
    });
    
    // Visa produkter grupperade efter kategori
    Object.entries(productsByCategory).forEach(([category, categoryProducts]) => {
        // Skapa kategoriheader
        const categoryHeader = document.createElement('div');
        categoryHeader.className = 'category-header';
        categoryHeader.textContent = category;
        categoryHeader.style.gridColumn = '1/-1';
        resultsElement.appendChild(categoryHeader);
        
        // Visa produkter i denna kategori
        categoryProducts.forEach(product => {
            const productCard = createProductCard(product);
            resultsElement.appendChild(productCard);
        });
    });
}

// Skapa ett produktkort
function createProductCard(product) {
    const productCard = document.createElement('div');
    productCard.className = 'product-card';
    
    // Skapa lagerstatusrader
    const stockItems = product.stockInfo.map(stock => {
        const store = stores.find(s => s.storeId === stock.storeId);
        
        // Markera primärbutik
        const isPrimary = primaryStore && store.storeId === primaryStore.storeId;
        
        return `
            <div class="stock-item ${isPrimary ? 'primary-store' : ''}">
                <div class="store-info">
                    <div class="store-name-small">${store.namn}</div>
                    <div class="store-city">${store.stad}</div>
                </div>
                <div class="stock-count">${stock.stock} st</div>
            </div>
        `;
    }).join('');
    
    productCard.innerHTML = `
        <div class="product-title">${product.title}</div>
        <div class="stock-info">
            ${stockItems}
        </div>
        <a href="${product.fullUrl}" class="product-link" target="_blank">Visa produkt</a>
    `;
    
    return productCard;
}

// Starta applikationen när DOM är laddad
document.addEventListener('DOMContentLoaded', initApp);
