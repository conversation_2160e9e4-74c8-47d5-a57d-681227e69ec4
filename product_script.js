// Produktanalys JavaScript
// Hårdkodat för p-4042909 (MacBook Air) som test

const PRODUCT_ID = 'p-4042909';
const API_BASE_URL = 'https://api.rytterfalk.com';

// DOM-element
let loadingElement, errorElement, contentElement;
let productTitleElement, productMetaElement;
let stockStatsElement, movementStatsElement, availabilityStatsElement, storeStatsElement;
let timelineElement;

// Initialisering
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    loadProductAnalysis();
});

function initializeElements() {
    loadingElement = document.getElementById('loading');
    errorElement = document.getElementById('error');
    contentElement = document.getElementById('content');

    productTitleElement = document.getElementById('product-title');
    productMetaElement = document.getElementById('product-meta');

    stockStatsElement = document.getElementById('stock-stats');
    movementStatsElement = document.getElementById('movement-stats');
    availabilityStatsElement = document.getElementById('availability-stats');
    storeStatsElement = document.getElementById('store-stats');

    timelineElement = document.getElementById('timeline');
}

async function loadProductAnalysis() {
    try {
        showLoading();

        // Hämta produktdata och historik parallellt
        const [productData, historyData] = await Promise.all([
            fetchProductData(),
            fetchProductHistory()
        ]);

        if (productData && historyData) {
            displayProductInfo(productData);
            analyzeAndDisplayData(productData, historyData);
            hideLoading();
        } else {
            throw new Error('Kunde inte hämta produktdata');
        }

    } catch (error) {
        console.error('Fel vid laddning av produktanalys:', error);
        showError('Kunde inte ladda produktanalys: ' + error.message);
    }
}

async function fetchProductData() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/stock?product_id=${PRODUCT_ID}`);
        if (!response.ok) throw new Error(`HTTP ${response.status}`);

        const data = await response.json();
        return data.products && data.products.length > 0 ? data.products[0] : null;
    } catch (error) {
        console.error('Fel vid hämtning av produktdata:', error);
        return null;
    }
}

async function fetchProductHistory() {
    try {
        // Simulerad historikdata för nu - senare kan vi skapa en riktig endpoint
        const response = await fetch(`${API_BASE_URL}/api/stock?product_id=${PRODUCT_ID}&history=30`);
        if (!response.ok) {
            // Om history-endpoint inte finns, simulera data
            return generateMockHistoryData();
        }

        const data = await response.json();
        return data.history || generateMockHistoryData();
    } catch (error) {
        console.error('Fel vid hämtning av historikdata:', error);
        return generateMockHistoryData();
    }
}

function generateMockHistoryData() {
    // Simulerad historikdata för demonstration
    const history = [];
    const stores = ['Stockholm City', 'Vällingby', 'Täby', 'Kungens Kurva', 'Barkarby'];
    const now = new Date();

    for (let i = 30; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);

        stores.forEach(store => {
            // Simulera realistiska lagerändringar
            const baseStock = Math.floor(Math.random() * 8) + 1;
            const change = Math.floor(Math.random() * 5) - 2; // -2 till +2

            history.push({
                date: date.toISOString().split('T')[0],
                time: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
                store: store,
                stock_before: Math.max(0, baseStock - change),
                stock_after: baseStock,
                change: change
            });
        });
    }

    return history.sort((a, b) => new Date(b.date + 'T' + b.time) - new Date(a.date + 'T' + a.time));
}

function displayProductInfo(product) {
    productTitleElement.textContent = product.title || 'MacBook Air 13" M3 256GB';
    productMetaElement.innerHTML = `
        <strong>Produkt-ID:</strong> ${PRODUCT_ID} |
        <strong>Totalt lager:</strong> ${calculateTotalStock(product)} st
    `;
}

function calculateTotalStock(product) {
    if (!product.stores) return 0;
    return product.stores.reduce((total, store) => total + (store.stock || 0), 0);
}

function analyzeAndDisplayData(product, history) {
    displayStockStats(product, history);
    displayMovementStats(history);
    displayAvailabilityStats(product, history);
    displayStoreStats(product, history);
    displayTimeline(history);
}

function displayStockStats(product, history) {
    const totalStock = calculateTotalStock(product);
    const storesWithStock = product.stores ? product.stores.filter(s => s.stock > 0).length : 0;
    const totalStores = product.stores ? product.stores.length : 0;
    const avgStock = totalStores > 0 ? (totalStock / totalStores).toFixed(1) : 0;

    stockStatsElement.innerHTML = `
        <div class="metric-row">
            <span class="metric-label">Totalt lager</span>
            <span class="metric-value">${totalStock} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Butiker med lager</span>
            <span class="metric-value">${storesWithStock}/${totalStores}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Genomsnittligt lager</span>
            <span class="metric-value">${avgStock} st/butik</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Tillgänglighet</span>
            <span class="metric-value ${totalStock > 0 ? 'positive' : 'negative'}">
                ${totalStock > 0 ? 'I lager' : 'Slut i lager'}
            </span>
        </div>
    `;
}

function displayMovementStats(history) {
    const movements = history.filter(h => h.change !== 0);
    const increases = movements.filter(h => h.change > 0);
    const decreases = movements.filter(h => h.change < 0);

    const totalIncrease = increases.reduce((sum, h) => sum + h.change, 0);
    const totalDecrease = Math.abs(decreases.reduce((sum, h) => sum + h.change, 0));

    movementStatsElement.innerHTML = `
        <div class="metric-row">
            <span class="metric-label">Totala rörelser</span>
            <span class="metric-value">${movements.length} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Påfyllningar</span>
            <span class="metric-value positive">+${totalIncrease} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Försäljning</span>
            <span class="metric-value negative">-${totalDecrease} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Nettorörelser</span>
            <span class="metric-value ${totalIncrease - totalDecrease >= 0 ? 'positive' : 'negative'}">
                ${totalIncrease - totalDecrease >= 0 ? '+' : ''}${totalIncrease - totalDecrease} st
            </span>
        </div>
    `;
}

function displayAvailabilityStats(product, history) {
    // Beräkna hur länge produkten varit slut i olika butiker
    const outOfStockPeriods = calculateOutOfStockPeriods(history);
    const avgOutOfStockDays = outOfStockPeriods.length > 0 ?
        (outOfStockPeriods.reduce((sum, p) => sum + p.days, 0) / outOfStockPeriods.length).toFixed(1) : 0;

    const restockEvents = history.filter(h => h.change > 0 && h.stock_before === 0).length;

    availabilityStatsElement.innerHTML = `
        <div class="metric-row">
            <span class="metric-label">Genomsnittlig sluttid</span>
            <span class="metric-value warning">${avgOutOfStockDays} dagar</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Påfyllningstillfällen</span>
            <span class="metric-value">${restockEvents} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Tillgänglighet %</span>
            <span class="metric-value positive">${calculateAvailabilityPercentage(history)}%</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Status</span>
            <span class="metric-value ${calculateTotalStock(product) > 0 ? 'positive' : 'negative'}">
                ${calculateTotalStock(product) > 0 ? 'Tillgänglig' : 'Ej tillgänglig'}
            </span>
        </div>
    `;
}

function displayStoreStats(product, history) {
    if (!product.stores) return;

    const bestStore = product.stores.reduce((best, store) =>
        (store.stock || 0) > (best.stock || 0) ? store : best, product.stores[0]);

    const storesLowStock = product.stores.filter(s => s.stock === 1).length;
    const storesOutOfStock = product.stores.filter(s => s.stock === 0).length;

    storeStatsElement.innerHTML = `
        <div class="metric-row">
            <span class="metric-label">Bästa butik</span>
            <span class="metric-value">${bestStore.name} (${bestStore.stock || 0} st)</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Lågt lager (1 st)</span>
            <span class="metric-value warning">${storesLowStock} butiker</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Slut i lager</span>
            <span class="metric-value negative">${storesOutOfStock} butiker</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Genomsnitt/butik</span>
            <span class="metric-value">${(calculateTotalStock(product) / product.stores.length).toFixed(1)} st</span>
        </div>
    `;
}

function displayTimeline(history) {
    const recentHistory = history.slice(0, 20); // Visa senaste 20 händelserna

    timelineElement.innerHTML = recentHistory.map(item => `
        <div class="timeline-item">
            <div class="timeline-date">${formatDate(item.date)}</div>
            <div class="timeline-store">${item.store}</div>
            <div class="timeline-change">
                ${item.change > 0 ?
                    `<span class="stock-increase">+${item.change} st (påfyllning)</span>` :
                    item.change < 0 ?
                    `<span class="stock-decrease">${item.change} st (försäljning)</span>` :
                    `<span class="stock-stable">Ingen förändring</span>`
                }
            </div>
            <div class="timeline-stock">${item.stock_after} st</div>
        </div>
    `).join('');
}

// Hjälpfunktioner
function calculateOutOfStockPeriods(history) {
    // Förenklad beräkning - kan förbättras
    return history.filter(h => h.stock_after === 0).map(h => ({ days: 1 }));
}

function toggleStore(storeId) {
    const sIdStr=storeId.toString();const idx=selectedStores.indexOf(sIdStr);
    if(idx>-1)selectedStores.splice(idx,1);else selectedStores.push(sIdStr);
    const card=storeSelectorDiv.querySelector(`[data-store-id="${sIdStr}"]`);
    if(card)card.classList.toggle('selected');
    if(selectedStores.length>0)loadStockData();
    else{if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Välj butiker.</h3></div>`;}
    updateLoadButton(); 
}

function updateLoadButton() {
    if (!loadButton) return; 
    if (currentStoreFilterMode === 'closest') {
        const cS=selectedStores.length>0?stores.find(s=>s.storeId.toString()===selectedStores[0]):null;
        loadButton.textContent=cS?`Uppdatera för ${cS.namn}`:(userLocation?'Ingen närmaste':'Hitta närmaste');
    } else if (currentStoreFilterMode === 'nearby') {
        loadButton.textContent=`Uppdatera för närliggande (${selectedStores.length}st)`;
    } else if (currentStoreFilterMode === 'all_stores') {
        loadButton.textContent=`Uppdatera för alla ${selectedStores.length} butiker`;
    } else { 
        loadButton.textContent=selectedStores.length===0?'Välj butiker':`Ladda lager (${selectedStores.length} valda)`;
    }
    loadButton.disabled = selectedStores.length === 0;
}

// --- NY/MODIFIERAD FUNKTIONALITET ---

function toggleDisplayMode() {
    currentDisplayMode = (currentDisplayMode === 'compact') ? 'advanced' : 'compact';
    if (viewModeToggleButton) {
        viewModeToggleButton.textContent = currentDisplayMode === 'advanced' ? 'Kompakt vy' : 'Avancerad vy';
        viewModeToggleButton.classList.toggle('active', currentDisplayMode === 'advanced');
    }
    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        updateSummaryView(displayedProductsCache);
    }
}

function setStockStatusFilter(mode) {
    stockStatusFilter = mode;
    updateActiveButton('.filter-buttons-compact button', `btn-stock-${mode}`);
    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        updateSummaryView(displayedProductsCache);
    } else if (selectedStores.length > 0) {
        loadStockData();
    }
}

function setSortOption(sortOption) {
    currentSortOption = sortOption;
    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        updateSummaryView(displayedProductsCache);
    }
}

function performSearch() {
    searchQuery = searchInput ? searchInput.value.toLowerCase().trim() : '';

    // Visa/dölj rensa-knappen
    if (clearSearchButton) {
        clearSearchButton.style.display = searchQuery ? 'flex' : 'none';
    }

    console.log('Söker efter:', searchQuery); // Debug

    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        // updateSummaryView anropas nu inifrån displayResults med filtrerade data
    }
}

function clearSearch() {
    if (searchInput) {
        searchInput.value = '';
    }
    searchQuery = '';
    if (clearSearchButton) {
        clearSearchButton.style.display = 'none';
    }

    console.log('Sökning rensad'); // Debug

    if (displayedProductsCache.length > 0) {
        displayResults(displayedProductsCache);
        // updateSummaryView anropas nu inifrån displayResults med filtrerade data
    }
}

function sortProducts(products, sortOption) {
    return products.sort((a, b) => {
        switch (sortOption) {
            case 'stock_desc':
                const totalStockA = a.stocks.reduce((sum, s) => sum + s.stock, 0);
                const totalStockB = b.stocks.reduce((sum, s) => sum + s.stock, 0);
                return totalStockB - totalStockA;

            case 'stock_asc':
                const totalStockA2 = a.stocks.reduce((sum, s) => sum + s.stock, 0);
                const totalStockB2 = b.stocks.reduce((sum, s) => sum + s.stock, 0);
                return totalStockA2 - totalStockB2;

            case 'name_asc':
                return a.title.localeCompare(b.title, 'sv');

            case 'name_desc':
                return b.title.localeCompare(a.title, 'sv');

            case 'price_desc':
                const priceA = typeof a.price === 'number' ? a.price : 0;
                const priceB = typeof b.price === 'number' ? b.price : 0;
                return priceB - priceA;

            case 'price_asc':
                const priceA2 = typeof a.price === 'number' ? a.price : 0;
                const priceB2 = typeof b.price === 'number' ? b.price : 0;
                return priceA2 - priceB2;

            default:
                return 0;
        }
    });
}

function filterProductsBySearch(products, query) {
    if (!query || query.length < 1) return products;

    console.log(`Filtrerar ${products.length} produkter med query: "${query}"`); // Debug

    const filtered = products.filter(product => {
        const titleMatch = product.title && product.title.toLowerCase().includes(query);
        const eanMatch = product.ean && product.ean.toLowerCase().includes(query);
        const skuMatch = product.sku && product.sku.toLowerCase().includes(query);
        const productIdMatch = product.productId && product.productId.toLowerCase().includes(query);

        const matches = titleMatch || eanMatch || skuMatch || productIdMatch;

        if (matches) {
            console.log(`Match hittad: ${product.title}`); // Debug
        }

        return matches;
    });

    console.log(`Filtrering klar: ${filtered.length} produkter matchade`); // Debug
    return filtered;
}

async function loadStockData() {
    console.log(`--- loadStockData (Kat: ${currentSelectedCategory || 'Alla'}, Filter: ${currentStoreFilterMode}, Butiker: ${selectedStores.join(',')}) ---`);
    if(loadingDiv)loadingDiv.style.display='block'; if(errorDiv)errorDiv.style.display='none'; if(resultsDiv)resultsDiv.innerHTML='';
    if(ALL_PRODUCTS_FROM_API.length===0){console.warn("ALL_PRODUCTS_FROM_API är tom.");if(errorDiv){errorDiv.textContent="Produktkatalog ej laddad.";errorDiv.style.display='block';}if(loadingDiv)loadingDiv.style.display='none';return;}
    if(selectedStores.length===0){console.warn(`Inga valda butiker för: ${currentStoreFilterMode}`);if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga butiker.</h3><p>Välj filter/butiker.</p></div>`;if(loadingDiv)loadingDiv.style.display='none';updateLoadButton();return;}

    const storeIdsParam=selectedStores.join(',');
    const apiUrl=`${API_BASE_URL}/api/current_stock`;
    let qParams=[];
    if(storeIdsParam)qParams.push(`store_ids=${storeIdsParam}`);
    if(currentSelectedCategory)qParams.push(`category=${encodeURIComponent(currentSelectedCategory)}`);
    const fullApiUrl=`${apiUrl}${qParams.length>0?'?':''}${qParams.join('&')}`;console.log("Anropar API:",fullApiUrl);

    try{
        const response=await fetch(fullApiUrl);
        if(!response.ok){const errTxt=await response.text();throw new Error(`API: ${response.status}. ${errTxt.substring(0,100)}`);}
        const apiData=await response.json();

        // Debug: Kontrollera om tidsstämplar finns
        console.log('API Data sample (första 3 objekt):', apiData.slice(0, 3));
        const timestampSample = apiData.find(item => item.timestamp);
        if (timestampSample) {
            console.log('Hittade tidsstämpel i API-data:', timestampSample.timestamp);
        } else {
            console.warn('Ingen tidsstämpel hittades i API-data!');
        }

        displayedProductsCache=[];
        const productMap=new Map();
        const prodsToConsider=currentSelectedCategory?ALL_PRODUCTS_FROM_API.filter(p=>p.category===currentSelectedCategory):ALL_PRODUCTS_FROM_API;

        // Extrahera tidsstämplar för att hitta senaste uppdatering
        let latestTimestamp = null;

        prodsToConsider.forEach(pS=>{productMap.set(pS.productId.toString(),{productId:pS.productId.toString(),title:pS.title,category:pS.category,price:pS.price,ean:pS.ean,sku:pS.sku,stocks:[]});});
        apiData.forEach(item=>{
            const pIdStr=item.productId.toString();
            if(productMap.has(pIdStr)){
                const sD=stores.find(s=>s.storeId.toString()===item.storeId.toString());
                const sN=sD?sD.namn:`Butik ${item.storeId}`;
                const sC=sD?sD.stad:'';
                productMap.get(pIdStr).stocks.push({store:{namn:sN,stad:sC,storeId:item.storeId},stock:item.stockCount});

                // Spåra senaste tidsstämpel
                if(item.timestamp) {
                    const itemTimestamp = new Date(item.timestamp);
                    if(!latestTimestamp || itemTimestamp > latestTimestamp) {
                        latestTimestamp = itemTimestamp;
                    }
                }
            }
        });
        displayedProductsCache=Array.from(productMap.values());

        // Uppdatera tidsstämpel-visning
        updateLastUpdateInfo(latestTimestamp);

        displayResults(displayedProductsCache);
    }catch(err){
        console.error("Fel i loadStockData:",err);let msg=err.message;if(err instanceof SyntaxError)msg="Svar från servern felaktig JSON.";
        if(errorDiv){errorDiv.textContent=`Kunde ej hämta: ${msg}`;errorDiv.style.display='block';}
        if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Fel</h3><p>${msg}</p></div>`;
    }finally{if(loadingDiv)loadingDiv.style.display='none';}
}

function displayResults(productsToDisplayInput) {
    if(!resultsDiv) return; resultsDiv.innerHTML = ''; let productsActuallyShown = 0;
    let productsToRender = JSON.parse(JSON.stringify(productsToDisplayInput));

    // Filtrera baserat på sökfråga
    productsToRender = filterProductsBySearch(productsToRender, searchQuery);

    // Lägg till subGroup för kategorisering
    productsToRender.forEach(p => { p.subGroup = getProductSubGroup(p.title, p.category); });

    // Gruppera produkter per kategori och subgrupp
    const productsByCategory = {};
    productsToRender.forEach(product => {
        const category = product.category || "Okategoriserad";
        const subGroup = product.subGroup || "Övriga";

        if (!productsByCategory[category]) {
            productsByCategory[category] = {};
        }
        if (!productsByCategory[category][subGroup]) {
            productsByCategory[category][subGroup] = [];
        }
        productsByCategory[category][subGroup].push(product);
    });

    // Sortera kategorier
    const desiredCategoryOrder = ["Mac", "iPad", "iPhone", "Watch", "AirPods", "ATV & HomePods", "Tillbehör"];
    const sortedCategories = Object.keys(productsByCategory).sort((a, b) => {
        const indexA = desiredCategoryOrder.indexOf(a);
        const indexB = desiredCategoryOrder.indexOf(b);
        if (indexA !== -1 && indexB !== -1) return indexA - indexB;
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;
        return a.localeCompare(b);
    });

    // Rendera produkter kategori för kategori
    sortedCategories.forEach(categoryName => {
        const subGroups = productsByCategory[categoryName];

        // Lägg till kategoriheader
        const catH2 = document.createElement('h2');
        catH2.textContent = categoryName;
        catH2.style.cssText = "grid-column:1/-1;color:white;margin-top:30px;margin-bottom:15px;text-align:left;padding-bottom:5px;border-bottom:2px solid rgba(255,255,255,0.5);font-size:1.6rem;";
        resultsDiv.appendChild(catH2);

        // Sortera subgrupper
        const sortedSubGroups = Object.keys(subGroups).sort();

        sortedSubGroups.forEach(subGroupName => {
            let productsInSubGroup = subGroups[subGroupName];

            // Sortera produkter inom subgruppen
            productsInSubGroup = sortProducts(productsInSubGroup, currentSortOption);

            // Lägg till subgrupp-header om det inte är "Övriga"
            if (subGroupName !== "Övriga") {
                const subH3 = document.createElement('h3');
                subH3.textContent = subGroupName;
                subH3.style.cssText = "grid-column:1/-1;color:rgba(255,255,255,0.85);margin-top:15px;margin-bottom:8px;text-align:left;padding-left:10px;font-size:1.2rem;font-weight:500;";
                resultsDiv.appendChild(subH3);
            }

            // Rendera produkter i subgruppen
            productsInSubGroup.forEach(product => {
                const hasStock = product.stocks.some(s => s.stock > 0);
                let showThisCard = false;
                if (stockStatusFilter === 'in_stock' && hasStock) showThisCard = true;
                if (stockStatusFilter === 'out_of_stock' && !hasStock) showThisCard = true;
                if (stockStatusFilter === 'all') showThisCard = true;

                if (showThisCard) {
                    const pCard = document.createElement('div');
                    pCard.className = 'product-card';
                    if (!hasStock) pCard.classList.add('out-of-stock-card');

                    const sItemsHTML = product.stocks.map(s => {
                        let stockItemClass = 'stock-item';
                        let stockCountClass = 'stock-count';

                        if (s.stock === 0) {
                            stockItemClass += ' out-of-stock-item';
                            stockCountClass += ' empty';
                        } else if (s.stock === 1) {
                            stockItemClass += ' low-stock-item';
                            stockCountClass += ' low-stock';
                        }

                        return `<div class="${stockItemClass}">
                            <div class="store-info"><div class="store-name-small">${s.store.namn}</div><div class="store-city">${s.store.stad}</div></div>
                            <div class="${stockCountClass}">${s.stock} st</div>
                        </div>`;
                    }).join('');

                    let detailsHTML = `<div class="product-details">Art.nr: ${product.productId}`;
                    if (product.ean && currentDisplayMode === 'advanced') detailsHTML += `    EAN: ${product.ean}`;
                    if (product.sku && currentDisplayMode === 'advanced') detailsHTML += `<br>SKU: ${product.sku}`;
                    if (typeof product.price === 'number' && product.price > 0 && currentDisplayMode === 'advanced') {
                        detailsHTML += `<br>Pris: ${product.price.toLocaleString('sv-SE', { style: 'currency', currency: 'SEK' })}`;
                    }
                    detailsHTML += `</div>`;

                    pCard.innerHTML = `<div class="product-title">${product.title}</div> ${detailsHTML} <div class="stock-info">${sItemsHTML.length > 0 ? sItemsHTML : '<p style="font-style:italic;color:#777;text-align:center;">Inget lager i valda butiker.</p>'}</div>`;
                    resultsDiv.appendChild(pCard);
                    productsActuallyShown++;
                }
            });
        });
    });

    if (productsActuallyShown === 0) {
        const noResultsMsg = searchQuery ?
            `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga produkter</h3><p>Inga produkter matchade sökningen "${searchQuery}" och dina filter.</p></div>` :
            `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga produkter</h3><p>Inga produkter matchade dina filter.</p></div>`;
        resultsDiv.innerHTML = noResultsMsg;
    } else {
        updateSummaryView(productsToRender); // Använd filtrerade listan för att reflektera aktuell sökning
    }
}

function updateSummaryView(productsToSummarize) {
    if (!summarySectionDiv || !summaryContentDiv) { console.error("Summerings-DOM-element saknas!"); return; }
    if (!productsToSummarize || productsToSummarize.length === 0) {
        summarySectionDiv.style.display = 'none'; return;
    }
    let totalUnitsInStock = 0; let totalStockValue = 0; const uniqueProductIdsWithStock = new Set(); const stockPerStore = {};
    productsToSummarize.forEach(product => {
        const price = typeof product.price === 'number' ? product.price : null;
        product.stocks.forEach(stockItem => {
            if (stockItem.stock > 0) {
                totalUnitsInStock += stockItem.stock; uniqueProductIdsWithStock.add(product.productId);
                if (price !== null) totalStockValue += stockItem.stock * price;
                const storeId = stockItem.store.storeId.toString();
                if (!stockPerStore[storeId]) stockPerStore[storeId] = { name: stockItem.store.namn, city: stockItem.store.stad, units: 0, value: 0 };
                stockPerStore[storeId].units += stockItem.stock;
                if (price !== null) stockPerStore[storeId].value += stockItem.stock * price;
            }
        });
    });
    const productsWithStockCount = uniqueProductIdsWithStock.size;
    let summaryHTML = `<dl class="summary-details-list">`;
    summaryHTML += `<dt>Totalt enheter i lager:</dt><dd>${totalUnitsInStock} st</dd>`;
    summaryHTML += `<dt>Antal unika produkter m. lager:</dt><dd>${productsWithStockCount}</dd>`;
    if (currentDisplayMode === 'advanced') {
        if (totalStockValue > 0) summaryHTML += `<dt>Uppskattat lagervärde:</dt><dd>${totalStockValue.toLocaleString('sv-SE',{style:'currency',currency:'SEK'})}</dd>`;
        summaryHTML += `</dl>`;
        if (Object.keys(stockPerStore).length > 1 && totalUnitsInStock > 0) {
            summaryHTML += `<h4 style="margin-top:15px;font-size:1em;">Lagerfördelning per butik:</h4>`;
            summaryHTML += `<table style="font-size:0.9em;width:100%;"><thead><tr><th>Butik</th><th>Enheter</th><th>Andel</th><th>Värde</th></tr></thead><tbody>`;
            const sortedStoresByUnits=Object.entries(stockPerStore).sort(([,a],[,b])=>b.units-a.units);
            for(const[,storeData]of sortedStoresByUnits){const pcent=totalUnitsInStock>0?((storeData.units/totalUnitsInStock)*100).toFixed(1):"0.0";summaryHTML+=`<tr><td>${storeData.name} (${storeData.city})</td><td>${storeData.units} st</td><td>${pcent}%</td><td>${storeData.value > 0 ? storeData.value.toLocaleString('sv-SE',{style:'currency',currency:'SEK'}):'-'}</td></tr>`;}
            summaryHTML+=`</tbody></table>`;
        } else if (Object.keys(stockPerStore).length===1&&totalUnitsInStock>0){const sId=Object.keys(stockPerStore)[0];const sData=stockPerStore[sId];if(sData.value>0)summaryHTML+=`<p style="margin-top:10px;"><strong>Lagervärde (${sData.name}):</strong> ${sData.value.toLocaleString('sv-SE',{style:'currency',currency:'SEK'})}</p>`;}
    } else { summaryHTML += `</dl>`; if(Object.keys(stockPerStore).length>0&&totalUnitsInStock>0){summaryHTML+=`<p style="font-size:0.9em;margin-top:10px;">`;let sParts=[];for(const sId in stockPerStore){if(stockPerStore[sId].units>0)sParts.push(`${stockPerStore[sId].name}: ${stockPerStore[sId].units}st`);}summaryHTML+=sParts.join(' | ');summaryHTML+=`</p>`;}}
    summaryContentDiv.innerHTML = summaryHTML; summarySectionDiv.style.display = 'block';
}

function updateLastUpdateInfo(timestamp) {
    const lastUpdateDiv = document.getElementById('last-update-info');
    if (!lastUpdateDiv) return;

    if (!timestamp) {
        lastUpdateDiv.style.display = 'none';
        return;
    }

    // Formatera tidsstämpeln på svenska
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Europe/Stockholm'
    };

    const formattedTime = timestamp.toLocaleDateString('sv-SE', options);

    // Beräkna hur länge sedan uppdateringen var
    const now = new Date();
    const diffMs = now - timestamp;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    let timeAgo = '';
    if (diffMinutes < 1) {
        timeAgo = 'mindre än en minut sedan';
    } else if (diffMinutes < 60) {
        timeAgo = `${diffMinutes} minut${diffMinutes !== 1 ? 'er' : ''} sedan`;
    } else if (diffHours < 24) {
        timeAgo = `${diffHours} timm${diffHours !== 1 ? 'ar' : 'e'} sedan`;
    } else if (diffDays < 7) {
        timeAgo = `${diffDays} dag${diffDays !== 1 ? 'ar' : ''} sedan`;
    } else {
        timeAgo = 'mer än en vecka sedan';
    }

    lastUpdateDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="margin-bottom: 3px;">📊 Senaste lageruppdatering:</div>
            <div style="font-weight: 500;">${formattedTime}</div>
            <div style="font-size: 0.75rem; opacity: 0.8;">(${timeAgo})</div>
        </div>
    `;
    lastUpdateDiv.style.display = 'block';
}

document.addEventListener('DOMContentLoaded', () => {
    if (viewModeToggleButton) {
        viewModeToggleButton.onclick = () => toggleDisplayMode();
        // Sätt initialt tillstånd för avancerad vy
        viewModeToggleButton.textContent = 'Kompakt vy';
        viewModeToggleButton.classList.add('active');
    }

    // Sökfunktionalitet
    if (searchInput) {
        // Realtidssökning med debounce
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            console.log('Sökinput ändrad:', e.target.value); // Debug
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 200); // 200ms debounce för snabbare respons
        });

        // Sök även när användaren trycker Enter
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                clearTimeout(searchTimeout);
                performSearch();
            }
        });

        // Fokusera sökrutan när användaren trycker Ctrl+F eller Cmd+F
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                searchInput.focus();
            }
        });
    }

    // Sätt initial sortering
    if (sortDropdown) {
        sortDropdown.value = currentSortOption;
    }

    initializeApp();
});
