// Produktanalys JavaScript

// Hämta produktID från URL-parameter eller använd default
function getProductIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('productId') || '4042909'; // Default till MacBook Air för test
}

const PRODUCT_ID = getProductIdFromUrl();
const API_BASE_URL = 'https://api.rytterfalk.com';

// DOM-element
let loadingElement, errorElement, contentElement;
let productTitleElement, productMetaElement;
let stockStatsElement, movementStatsElement, availabilityStatsElement, storeStatsElement;
let timelineElement;
let stockChart;

// Initialisering
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    loadProductAnalysis();
});

function initializeElements() {
    loadingElement = document.getElementById('loading');
    errorElement = document.getElementById('error');
    contentElement = document.getElementById('content');

    productTitleElement = document.getElementById('product-title');
    productMetaElement = document.getElementById('product-meta');

    stockStatsElement = document.getElementById('stock-stats');
    movementStatsElement = document.getElementById('movement-stats');
    availabilityStatsElement = document.getElementById('availability-stats');
    storeStatsElement = document.getElementById('store-stats');

    timelineElement = document.getElementById('timeline');
}

async function loadProductAnalysis() {
    try {
        showLoading();

        // Hämta produktdata och historik från samma endpoint
        const productData = await fetchProductData();
        const historyData = await fetchProductHistory();

        if (productData && historyData) {
            displayProductInfo(productData);
            analyzeAndDisplayData(productData, historyData);
            hideLoading();
        } else {
            throw new Error('Kunde inte hämta produktdata');
        }

    } catch (error) {
        console.error('Fel vid laddning av produktanalys:', error);
        showError('Kunde inte ladda produktanalys: ' + error.message);
    }
}

async function fetchProductData() {
    try {
        // Hämta produktdata från den nya history-endpointen som innehåller både produkt och aktuell stock
        const response = await fetch(`${API_BASE_URL}/api/product_history/${PRODUCT_ID}?days=30`);
        if (!response.ok) throw new Error(`HTTP ${response.status}`);

        const data = await response.json();

        // Konvertera till det format som förväntas av displayProductInfo
        if (data.product && data.current_stock) {
            return {
                ...data.product,
                stores: data.current_stock.map(stock => ({
                    name: `Butik ${stock.storeId}`, // Vi kan förbättra detta senare med riktiga butiksnamn
                    stock: stock.stockCount,
                    storeId: stock.storeId
                }))
            };
        }
        return null;
    } catch (error) {
        console.error('Fel vid hämtning av produktdata:', error);
        return null;
    }
}

async function fetchProductHistory() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/product_history/${PRODUCT_ID}?days=30`);
        if (!response.ok) {
            console.warn('History-endpoint inte tillgänglig, använder mock-data');
            return generateMockHistoryData();
        }

        const data = await response.json();
        return data.history || generateMockHistoryData();
    } catch (error) {
        console.error('Fel vid hämtning av historikdata:', error);
        return generateMockHistoryData();
    }
}

function generateMockHistoryData() {
    // Simulerad historikdata för demonstration
    const history = [];
    const stores = ['Stockholm City', 'Vällingby', 'Täby', 'Kungens Kurva', 'Barkarby'];
    const now = new Date();

    for (let i = 30; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);

        stores.forEach(store => {
            // Simulera realistiska lagerändringar
            const baseStock = Math.floor(Math.random() * 8) + 1;
            const change = Math.floor(Math.random() * 5) - 2; // -2 till +2

            history.push({
                date: date.toISOString().split('T')[0],
                time: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
                store: store,
                stock_before: Math.max(0, baseStock - change),
                stock_after: baseStock,
                change: change
            });
        });
    }

    return history.sort((a, b) => new Date(b.date + 'T' + b.time) - new Date(a.date + 'T' + a.time));
}

function displayProductInfo(product) {
    productTitleElement.textContent = product.title || `Produkt ${PRODUCT_ID}`;
    productMetaElement.innerHTML = `
        <strong>Produkt-ID:</strong> ${PRODUCT_ID} |
        <strong>Totalt lager:</strong> ${calculateTotalStock(product)} st
    `;
}

function calculateTotalStock(product) {
    if (!product.stores) return 0;
    return product.stores.reduce((total, store) => total + (store.stock || 0), 0);
}

function analyzeAndDisplayData(product, history) {
    displayStockStats(product, history);
    displayMovementStats(history);
    displayAvailabilityStats(product, history);
    displayStoreStats(product, history);
    displayTimeline(history);
    createStockChart(history);
}

function displayStockStats(product, history) {
    const totalStock = calculateTotalStock(product);
    const storesWithStock = product.stores ? product.stores.filter(s => s.stock > 0).length : 0;
    const totalStores = product.stores ? product.stores.length : 0;
    const avgStock = totalStores > 0 ? (totalStock / totalStores).toFixed(1) : 0;

    stockStatsElement.innerHTML = `
        <div class="metric-row">
            <span class="metric-label">Totalt lager</span>
            <span class="metric-value">${totalStock} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Butiker med lager</span>
            <span class="metric-value">${storesWithStock}/${totalStores}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Genomsnittligt lager</span>
            <span class="metric-value">${avgStock} st/butik</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Tillgänglighet</span>
            <span class="metric-value ${totalStock > 0 ? 'positive' : 'negative'}">
                ${totalStock > 0 ? 'I lager' : 'Slut i lager'}
            </span>
        </div>
    `;
}

function displayMovementStats(history) {
    const movements = history.filter(h => h.change !== 0);
    const increases = movements.filter(h => h.change > 0);
    const decreases = movements.filter(h => h.change < 0);

    const totalIncrease = increases.reduce((sum, h) => sum + h.change, 0);
    const totalDecrease = Math.abs(decreases.reduce((sum, h) => sum + h.change, 0));

    movementStatsElement.innerHTML = `
        <div class="metric-row">
            <span class="metric-label">Totala rörelser</span>
            <span class="metric-value">${movements.length} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Påfyllningar</span>
            <span class="metric-value positive">+${totalIncrease} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Försäljning</span>
            <span class="metric-value negative">-${totalDecrease} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Nettorörelser</span>
            <span class="metric-value ${totalIncrease - totalDecrease >= 0 ? 'positive' : 'negative'}">
                ${totalIncrease - totalDecrease >= 0 ? '+' : ''}${totalIncrease - totalDecrease} st
            </span>
        </div>
    `;
}

function displayAvailabilityStats(product, history) {
    // Beräkna hur länge produkten varit slut i olika butiker
    const outOfStockPeriods = calculateOutOfStockPeriods(history);
    const avgOutOfStockDays = outOfStockPeriods.length > 0 ?
        (outOfStockPeriods.reduce((sum, p) => sum + p.days, 0) / outOfStockPeriods.length).toFixed(1) : 0;

    const restockEvents = history.filter(h => h.change > 0 && h.stock_before === 0).length;

    availabilityStatsElement.innerHTML = `
        <div class="metric-row">
            <span class="metric-label">Genomsnittlig sluttid</span>
            <span class="metric-value warning">${avgOutOfStockDays} dagar</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Påfyllningstillfällen</span>
            <span class="metric-value">${restockEvents} st</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Tillgänglighet %</span>
            <span class="metric-value positive">${calculateAvailabilityPercentage(history)}%</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Status</span>
            <span class="metric-value ${calculateTotalStock(product) > 0 ? 'positive' : 'negative'}">
                ${calculateTotalStock(product) > 0 ? 'Tillgänglig' : 'Ej tillgänglig'}
            </span>
        </div>
    `;
}

function displayStoreStats(product, history) {
    if (!product.stores) return;

    const bestStore = product.stores.reduce((best, store) =>
        (store.stock || 0) > (best.stock || 0) ? store : best, product.stores[0]);

    const storesLowStock = product.stores.filter(s => s.stock === 1).length;
    const storesOutOfStock = product.stores.filter(s => s.stock === 0).length;

    storeStatsElement.innerHTML = `
        <div class="metric-row">
            <span class="metric-label">Bästa butik</span>
            <span class="metric-value">${bestStore.name} (${bestStore.stock || 0} st)</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Lågt lager (1 st)</span>
            <span class="metric-value warning">${storesLowStock} butiker</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Slut i lager</span>
            <span class="metric-value negative">${storesOutOfStock} butiker</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">Genomsnitt/butik</span>
            <span class="metric-value">${(calculateTotalStock(product) / product.stores.length).toFixed(1)} st</span>
        </div>
    `;
}

function displayTimeline(history) {
    const recentHistory = history.slice(0, 20); // Visa senaste 20 händelserna

    timelineElement.innerHTML = recentHistory.map(item => `
        <div class="timeline-item">
            <div class="timeline-date">${formatDate(item.date)}</div>
            <div class="timeline-store">Butik ${item.store_id || item.store}</div>
            <div class="timeline-change">
                ${item.change > 0 ?
                    `<span class="stock-increase">+${item.change} st (påfyllning)</span>` :
                    item.change < 0 ?
                    `<span class="stock-decrease">${item.change} st (försäljning)</span>` :
                    `<span class="stock-stable">Ingen förändring</span>`
                }
            </div>
            <div class="timeline-stock">${item.stock_after} st</div>
        </div>
    `).join('');
}

// Hjälpfunktioner
function calculateOutOfStockPeriods(history) {
    // Förenklad beräkning - kan förbättras
    return history.filter(h => h.stock_after === 0).map(h => ({ days: 1 }));
}

function calculateAvailabilityPercentage(history) {
    const totalEntries = history.length;
    const inStockEntries = history.filter(h => h.stock_after > 0).length;
    return totalEntries > 0 ? Math.round((inStockEntries / totalEntries) * 100) : 0;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('sv-SE', {
        month: 'short',
        day: 'numeric'
    });
}

function showLoading() {
    loadingElement.style.display = 'block';
    errorElement.style.display = 'none';
    contentElement.style.display = 'none';
}

function hideLoading() {
    loadingElement.style.display = 'none';
    contentElement.style.display = 'block';
}

function showError(message) {
    loadingElement.style.display = 'none';
    contentElement.style.display = 'none';
    errorElement.style.display = 'block';
    errorElement.textContent = message;
}

function createStockChart(history) {
    const ctx = document.getElementById('stockChart');
    if (!ctx) return;

    // Förstör befintligt diagram om det finns
    if (stockChart) {
        stockChart.destroy();
    }

    // Gruppera data per butik och datum
    const storeData = {};
    const allDates = new Set();

    history.forEach(entry => {
        const storeId = entry.store_id || entry.store;
        const date = entry.date;

        if (!storeData[storeId]) {
            storeData[storeId] = {};
        }
        storeData[storeId][date] = entry.stock_after;
        allDates.add(date);
    });

    // Sortera datum
    const sortedDates = Array.from(allDates).sort();

    // Skapa dataset för varje butik
    const datasets = [];
    const colors = [
        '#667eea', '#764ba2', '#f093fb', '#f5576c',
        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
        '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
    ];

    let colorIndex = 0;
    Object.keys(storeData).forEach(storeId => {
        const data = sortedDates.map(date => storeData[storeId][date] || 0);

        datasets.push({
            label: `Butik ${storeId}`,
            data: data,
            borderColor: colors[colorIndex % colors.length],
            backgroundColor: colors[colorIndex % colors.length] + '20',
            borderWidth: 2,
            fill: false,
            tension: 0.1
        });
        colorIndex++;
    });

    // Skapa diagrammet
    stockChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: sortedDates.map(date => formatDate(date)),
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Lagerutveckling över tid',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    color: '#2c3e50'
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Antal i lager'
                    },
                    ticks: {
                        stepSize: 1
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}








