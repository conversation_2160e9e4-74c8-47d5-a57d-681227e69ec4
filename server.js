// server.js (FULL PROXY - MED ROBUSTARE OPTIONS-HANTERARE)
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const app = express();

// --------------- Konfiguration ---------------
// Se till att denna matchar adressen där din power_stock_checker.html serveras från http-server
const clientOrigin = 'http://************:8080'; 
// Du kan också prova 'http://127.0.0.1:8080' om du öppnar HTML-sidan via den adressen.
// Eller om du vill vara helt säker på att alla lokala anrop från din maskin fungerar:
// const clientOrigin = `http://${req.headers.host.split(':')[0]}:8080`; // Detta är mer avancerat, håll dig till en fast för nu.

const targetServer = 'https://power.se';
const proxyPort = 3000;
// -------------------------------------------

console.log(`[INFO] Client origin (förväntad frontend-adress): ${clientOrigin}`);
console.log(`[INFO] Target server för proxying: ${targetServer}`);
console.log(`[INFO] Proxy server kommer att lyssna på port: ${proxyPort}`);

// Middleware för att hantera pre-flight OPTIONS-requests med reguljärt uttryck
// Detta matchar /api/p/ följt av vad som helst
// **ÄNDRAD: Middleware för att hantera pre-flight OPTIONS-requests med reguljärt uttryck**
// Detta matchar /api/p/ följt av vad som helst
app.options(/^\/api\/p\/.*/, (req, res) => { 
    const requestOrigin = req.headers.origin;
    console.log(`[${new Date().toISOString()}] OPTIONS REQ: Mottog OPTIONS-förfrågan för ${req.originalUrl} från origin ${requestOrigin}`);
    
    if (requestOrigin === clientOrigin || requestOrigin === 'http://127.0.0.1:8080' || requestOrigin === `http://${req.ip}:8080`) {
        res.setHeader('Access-Control-Allow-Origin', requestOrigin);
    } else {
        console.warn(`[${new Date().toISOString()}] OPTIONS REQ: Origin '${requestOrigin}' matchar inte konfigurerad clientOrigin '${clientOrigin}'. Svarar ändå med konfigurerad clientOrigin för test.`);
        res.setHeader('Access-Control-Allow-Origin', clientOrigin);
    }
    
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization'); 
    res.setHeader('Access-Control-Max-Age', '86400'); 
    res.sendStatus(204); 
});

// Proxy-middleware för alla GET-anrop som börjar med /api/p/
app.use('/api/p', createProxyMiddleware({
    target: targetServer,
    changeOrigin: true, // Viktigt för virtuella hostar och för att sätta Host-headern korrekt
    pathRewrite: (path, req) => {
        const incomingPath = req.originalUrl; // Använd originalUrl för att få hela sökvägen som klienten skickade
        let productId = null;
        
        // Regex för att extrahera produkt-ID från sökvägar som /api/p/12345 eller /api/p/12345/
        const match = incomingPath.match(/^\/api\/p\/([^\/]+)\/?$/);
        if (match && match[1]) {
            productId = match[1];
        }

        if (productId) {
            const newPath = `/api/sparoute/x/p-${productId}/`;
            console.log(`[${new Date().toISOString()}] DYNAMIC PROXY PATH REWRITE: Incoming '${incomingPath}' -> ProductID '${productId}' -> New Target Path '${newPath}'`);
            return newPath;
        } else {
            // Om vi inte kunde extrahera ett produkt-ID, logga och skicka till en sökväg som troligen ger 404
            console.warn(`[${new Date().toISOString()}] DYNAMIC PROXY PATH REWRITE: Kunde inte extrahera ProductID från '${incomingPath}'. Anrop skickas till en felaktig sökväg.`);
            return '/FEJL_KUNDE_INTE_EXTRAHERA_PRODUKT_ID'; // Detta kommer garanterat ge 404 från Power (eller din egen felhantering)
        }
    },
    followRedirects: true, // Låt proxyn hantera eventuella omdirigeringar från målservern
    
    onProxyReq: (proxyReq, req, res) => {
        const fullTargetPath = proxyReq.protocol + '//' + proxyReq.host + proxyReq.path;
        console.log(`[${new Date().toISOString()}] DYNAMIC PROXY REQ: Skickar ${req.method}-förfrågan från ${req.originalUrl} (klient IP: ${req.ip || req.socket.remoteAddress}) till ${fullTargetPath}`);
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36');
    },

    onProxyRes: (proxyRes, req, res) => {
        const requestOrigin = req.headers.origin || clientOrigin; // Fallback till konfigurerad clientOrigin
        console.log(`[${new Date().toISOString()}] DYNAMIC PROXY RES: Mottog svar för ${req.originalUrl} från målservern med status: ${proxyRes.statusCode}. Sätter ACAO för ${requestOrigin}`);
        
        // Ta bort eventuella befintliga CORS-headers från målservern för att undvika konflikter
        delete proxyRes.headers['access-control-allow-origin'];
        delete proxyRes.headers['access-control-allow-methods'];
        delete proxyRes.headers['access-control-allow-headers'];
        delete proxyRes.headers['access-control-allow-credentials'];

        // Sätt våra egna CORS-headers på svaret från denna proxy TILLBAKA till din klient
        if (requestOrigin === clientOrigin || requestOrigin === 'http://127.0.0.1:8080' || requestOrigin === `http://${req.ip}:8080`) {
             res.setHeader('Access-Control-Allow-Origin', requestOrigin);
        } else {
            console.warn(`[${new Date().toISOString()}] DYNAMIC PROXY RES: Origin '${requestOrigin}' för GET matchar inte konfigurerad clientOrigin '${clientOrigin}'. Svarar ändå med konfigurerad clientOrigin för test.`);
            res.setHeader('Access-Control-Allow-Origin', clientOrigin); // Eller '*' för vidöppen testning
        }
        res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS'); 
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    },

    onError: (err, req, res) => {
        const requestOrigin = req.headers.origin || clientOrigin;
        console.error(`[${new Date().toISOString()}] DYNAMIC PROXY ERROR för ${req.originalUrl}:`, err.message);
        
        if (!res.headersSent) {
            res.setHeader('Access-Control-Allow-Origin', requestOrigin); // Försök sätta även vid fel
            res.writeHead(502, { 'Content-Type': 'text/plain' }); 
        }
        
        if (res.writable && !res.writableEnded) {
            res.end('Proxy error: Kunde inte ansluta till måltjänsten eller annat fel uppstod.');
        } else if (!res.writableEnded) {
            console.error(`[${new Date().toISOString()}] DYNAMIC PROXY ERROR: Kunde inte skriva felsvar till klienten för ${req.originalUrl}, socketen kan vara stängd.`);
        }
    },
    logLevel: 'info' // Ändra till 'debug' för mycket mer detaljerad loggning
}));

// En enkel route för att testa att servern är igång
app.get('/', (req, res) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ROOT: Mottog GET / från ${req.ip}`);
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.status(200).send(`Fullständig Proxy Server (med Regex OPTIONS v2) är igång på port ${proxyPort}. Tid: ${timestamp}`);
});

app.listen(proxyPort, '0.0.0.0', () => { // Lyssna på alla nätverksinterface
    console.log(`[SUCCESS] FULLSTÄNDIG PROXY SERVER (med Regex OPTIONS v2) har startat och lyssnar på http://localhost:${proxyPort} och på din dators IP-adress (t.ex. http://************:${proxyPort})`);
    console.log(`          Din frontend på ${clientOrigin} bör nu kunna anropa http://localhost:${proxyPort}/api/p/PRODUCT_ID/`);
});