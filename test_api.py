#!/usr/bin/env python3
"""
Script för att testa Power Stock API:et
"""

import requests
import json
from datetime import datetime

# Konfigurera API-URL (ändra till din server)
API_BASE_URL = "http://api.rytterfalk.com"  # eller din lokala adress

def test_api_endpoint(endpoint, params=None):
    """Testar en API-endpoint"""
    url = f"{API_BASE_URL}{endpoint}"
    
    print(f"🔗 Testar: {url}")
    if params:
        print(f"📋 Parametrar: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📦 Antal objekt: {len(data) if isinstance(data, list) else 'N/A'}")
            
            # Visa exempel på data
            if isinstance(data, list) and len(data) > 0:
                print(f"🔍 Första objektet:")
                first_item = data[0]
                for key, value in first_item.items():
                    print(f"   {key}: {value}")
                
                # Kontrollera tidsstämplar specifikt
                timestamps = [item.get('timestamp') for item in data if item.get('timestamp')]
                if timestamps:
                    print(f"\n⏰ Tidsstämplar hittade: {len(timestamps)}")
                    print(f"   Senaste: {max(timestamps)}")
                    print(f"   Äldsta: {min(timestamps)}")
                else:
                    print(f"\n❌ Inga tidsstämplar hittades i API-svaret!")
            
            return data
        else:
            print(f"❌ Fel: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Anslutningsfel: {e}")
        return None

def main():
    print("🧪 Power Stock API Test")
    print("=" * 50)
    
    # Test 1: Grundläggande API-status
    print("\n1️⃣ Testar grundläggande API-anslutning:")
    test_api_endpoint("/")
    
    print("\n" + "-" * 30)
    
    # Test 2: Produkter
    print("\n2️⃣ Testar produkter:")
    products = test_api_endpoint("/api/products")
    
    print("\n" + "-" * 30)
    
    # Test 3: Lagerstatus (alla)
    print("\n3️⃣ Testar lagerstatus (alla produkter):")
    stock_all = test_api_endpoint("/api/current_stock")
    
    print("\n" + "-" * 30)
    
    # Test 4: Lagerstatus för specifik kategori
    print("\n4️⃣ Testar lagerstatus (iPhone):")
    stock_iphone = test_api_endpoint("/api/current_stock", {"category": "iPhone"})
    
    print("\n" + "-" * 30)
    
    # Test 5: Lagerstatus för specifika butiker
    print("\n5️⃣ Testar lagerstatus (specifika butiker):")
    stock_stores = test_api_endpoint("/api/current_stock", {"store_ids": "1,2,3"})
    
    print("\n" + "=" * 50)
    print("📋 SAMMANFATTNING:")
    
    if products:
        print(f"   ✅ Produkter: {len(products)} st")
    else:
        print(f"   ❌ Produkter: Fel")
    
    if stock_all:
        print(f"   ✅ Lagerstatus (alla): {len(stock_all)} poster")
        # Kontrollera tidsstämplar
        timestamps = [item.get('timestamp') for item in stock_all if item.get('timestamp')]
        if timestamps:
            latest = max(timestamps)
            print(f"   ⏰ Senaste uppdatering: {latest}")
            
            # Beräkna hur länge sedan
            try:
                latest_dt = datetime.fromisoformat(latest.replace('Z', '+00:00'))
                now = datetime.now()
                diff = now - latest_dt.replace(tzinfo=None)
                hours_ago = diff.total_seconds() / 3600
                print(f"   🕒 Det var {hours_ago:.1f} timmar sedan")
            except:
                print(f"   ⚠️  Kunde inte beräkna tid")
        else:
            print(f"   ❌ Inga tidsstämplar i lagerstatus!")
    else:
        print(f"   ❌ Lagerstatus: Fel")

if __name__ == "__main__":
    main()
