<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produktanalys - Power Stock</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Specifika stilar för produktsidan */
        .product-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .product-title-large {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .product-meta {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .analysis-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .analysis-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .metric-row:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #555;
        }

        .metric-value {
            font-weight: 700;
            color: #2c3e50;
        }

        .metric-value.positive {
            color: #27ae60;
        }

        .metric-value.negative {
            color: #e74c3c;
        }

        .metric-value.warning {
            color: #f39c12;
        }

        .timeline-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .timeline-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .timeline-item:last-child {
            border-bottom: none;
        }

        .timeline-date {
            min-width: 120px;
            font-weight: 600;
            color: #667eea;
        }

        .timeline-store {
            min-width: 150px;
            font-weight: 500;
        }

        .timeline-change {
            flex: 1;
            padding: 0 15px;
        }

        .timeline-stock {
            min-width: 80px;
            text-align: right;
            font-weight: 700;
        }

        .stock-increase {
            color: #27ae60;
        }

        .stock-decrease {
            color: #e74c3c;
        }

        .stock-stable {
            color: #7f8c8d;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-style: italic;
            margin: 20px 0;
        }

        .back-button {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: background 0.2s;
        }

        .back-button:hover {
            background: #5a6268;
        }

        .loading-spinner {
            text-align: center;
            padding: 50px;
            color: white;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-button">← Tillbaka till huvudsidan</a>

        <div id="loading" class="loading-spinner">
            <h2>Laddar produktanalys...</h2>
            <p>Analyserar lagerhistorik för MacBook Air...</p>
        </div>

        <div id="error" class="error-message" style="display: none;">
            <!-- Felmeddelanden visas här -->
        </div>

        <div id="content" style="display: none;">
            <div class="product-header">
                <div class="product-title-large" id="product-title">
                    <!-- Produkttitel laddas här -->
                </div>
                <div class="product-meta" id="product-meta">
                    <!-- Produktmeta laddas här -->
                </div>
            </div>

            <div class="analysis-grid">
                <div class="analysis-card">
                    <h3>📊 Lagerstatistik</h3>
                    <div id="stock-stats">
                        <!-- Lagerstatistik laddas här -->
                    </div>
                </div>

                <div class="analysis-card">
                    <h3>📈 Rörelseanalys</h3>
                    <div id="movement-stats">
                        <!-- Rörelsestatistik laddas här -->
                    </div>
                </div>

                <div class="analysis-card">
                    <h3>⏱️ Tillgänglighetsanalys</h3>
                    <div id="availability-stats">
                        <!-- Tillgänglighetsstatistik laddas här -->
                    </div>
                </div>

                <div class="analysis-card">
                    <h3>🏪 Butiksanalys</h3>
                    <div id="store-stats">
                        <!-- Butiksstatistik laddas här -->
                    </div>
                </div>
            </div>

            <div class="timeline-container">
                <h3>📅 Lagerhistorik (Senaste 30 dagarna)</h3>
                <div class="chart-placeholder">
                    Diagram kommer här (kan implementeras med Chart.js senare)
                </div>
                <div id="timeline">
                    <!-- Tidslinje laddas här -->
                </div>
            </div>
        </div>
    </div>

    <script src="product_script.js"></script>
</body>
</html>