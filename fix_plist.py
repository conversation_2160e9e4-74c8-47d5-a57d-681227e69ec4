#!/usr/bin/env python3
"""
Script för att diagnostisera och fixa plist-filer
"""

import plistlib
import os
import shutil
from datetime import datetime

# Konfiguration
PLIST_DIRECTORY = '/home/<USER>/power_scraper/'
LOCAL_PLIST_DIRECTORY = '.'  # Nuvarande katalog
PLIST_FILES = [
    "iphone.plist", "ipad.plist", "mac.plist", "airpods.plist",
    "watch.plist", "appletv.plist", "homepod.plist", "tillbehor.plist"
]

def check_plist_file(filepath):
    """Kontrollerar om en plist-fil är giltig"""
    try:
        with open(filepath, 'rb') as fp:
            data = plistlib.load(fp)
            if isinstance(data, list):
                print(f"✅ {filepath}: Giltig plist med {len(data)} objekt")
                return True, len(data)
            else:
                print(f"❌ {filepath}: Inte en lista, typ: {type(data)}")
                return False, 0
    except FileNotFoundError:
        print(f"❌ {filepath}: Filen finns inte")
        return False, 0
    except Exception as e:
        print(f"❌ {filepath}: Fel vid läsning - {e}")
        return False, 0

def backup_file(filepath):
    """Skapar en backup av en fil"""
    if os.path.exists(filepath):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{filepath}.backup_{timestamp}"
        shutil.copy2(filepath, backup_path)
        print(f"📁 Backup skapad: {backup_path}")
        return backup_path
    return None

def copy_local_to_server():
    """Kopierar lokala plist-filer till servern"""
    if not os.path.exists(PLIST_DIRECTORY):
        print(f"❌ Servermappen {PLIST_DIRECTORY} finns inte!")
        return False
    
    success_count = 0
    for filename in PLIST_FILES:
        local_path = os.path.join(LOCAL_PLIST_DIRECTORY, filename)
        server_path = os.path.join(PLIST_DIRECTORY, filename)
        
        if os.path.exists(local_path):
            # Kontrollera att lokal fil är giltig
            is_valid, count = check_plist_file(local_path)
            if is_valid:
                # Backup server-fil om den finns
                if os.path.exists(server_path):
                    backup_file(server_path)
                
                # Kopiera lokal fil till server
                try:
                    shutil.copy2(local_path, server_path)
                    print(f"✅ Kopierade {filename} till servern ({count} objekt)")
                    success_count += 1
                except Exception as e:
                    print(f"❌ Kunde inte kopiera {filename}: {e}")
            else:
                print(f"⚠️  Hoppar över {filename} - lokal fil är inte giltig")
        else:
            print(f"⚠️  Lokal fil {filename} finns inte")
    
    return success_count == len(PLIST_FILES)

def main():
    print("🔧 Plist-fil diagnostik och reparation")
    print("=" * 50)
    
    # Kontrollera lokala filer
    print("\n📁 Kontrollerar lokala plist-filer:")
    local_valid = 0
    for filename in PLIST_FILES:
        local_path = os.path.join(LOCAL_PLIST_DIRECTORY, filename)
        is_valid, count = check_plist_file(local_path)
        if is_valid:
            local_valid += 1
    
    print(f"\n📊 Lokala filer: {local_valid}/{len(PLIST_FILES)} giltiga")
    
    # Kontrollera server-filer
    print(f"\n📁 Kontrollerar server-filer i {PLIST_DIRECTORY}:")
    server_valid = 0
    for filename in PLIST_FILES:
        server_path = os.path.join(PLIST_DIRECTORY, filename)
        is_valid, count = check_plist_file(server_path)
        if is_valid:
            server_valid += 1
    
    print(f"\n📊 Server-filer: {server_valid}/{len(PLIST_FILES)} giltiga")
    
    # Föreslå åtgärd
    if local_valid > server_valid:
        print(f"\n💡 REKOMMENDATION: Kopiera lokala filer till servern")
        print(f"   Lokala filer verkar vara bättre ({local_valid} vs {server_valid} giltiga)")
        
        response = input("\nVill du kopiera lokala filer till servern? (y/N): ")
        if response.lower() in ['y', 'yes', 'ja', 'j']:
            print("\n🚀 Kopierar filer...")
            if copy_local_to_server():
                print("\n✅ Alla filer kopierade framgångsrikt!")
                print("Nu kan du köra get_power_stock.py igen.")
            else:
                print("\n❌ Några filer kunde inte kopieras.")
        else:
            print("\n⏸️  Ingen åtgärd vidtagen.")
    
    elif server_valid == len(PLIST_FILES):
        print(f"\n✅ Alla server-filer är giltiga! Problemet kan vara något annat.")
        print(f"   Kontrollera att get_power_stock.py använder rätt sökväg.")
    
    else:
        print(f"\n⚠️  Både lokala och server-filer har problem.")
        print(f"   Du behöver skaffa giltiga plist-filer från en annan källa.")

if __name__ == "__main__":
    main()
