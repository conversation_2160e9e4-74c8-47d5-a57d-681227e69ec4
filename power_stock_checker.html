<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power Lagerstatus</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .location-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .location-icon {
            width: 40px;
            height: 40px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .store-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .store-card {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .store-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .store-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .store-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .store-address {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .distance {
            color: #667eea;
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 5px;
        }

        .load-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            width: 100%;
        }

        .load-button:hover {
            transform: translateY(-2px);
        }

        .load-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .stock-info {
            display: grid;
            gap: 10px;
        }

        .stock-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .store-info {
            flex: 1;
        }

        .store-name-small {
            font-weight: 500;
            color: #2c3e50;
        }

        .store-city {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .stock-count {
            background: #28a745;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: white;
            font-size: 1.2rem;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .store-selector {
                grid-template-columns: 1fr;
            }
            
            .results {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Power Lagerstatus</h1>
            <p>Intern portal för lagerkoll - HomePod test</p>
        </div>

        <div class="controls">
            <div class="location-info">
                <div class="location-icon">📍</div>
                <div>
                    <div id="location-status">Söker din position...</div>
                    <div style="font-size: 0.9rem; color: #6c757d;">Välj dina butiker nedan</div>
                </div>
            </div>

            <div class="store-selector" id="store-selector">
                <!-- Butiker läggs till här dynamiskt -->
            </div>

            <button class="load-button" id="load-button" onclick="loadStockData()">
                Ladda lagerstatus
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            Hämtar lagerstatus...
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="results" class="results"></div>
    </div>

    <script>
        // Butiker från JSON-filen
        const stores = [
            {"storeId": 7152, "namn": "POWER Torpavallen", "adress": "Torpavallsgatan 4D", "postnummer": "41673", "stad": "Göteborg", "region": "Västra Götaland", "lat": 57.6792, "lng": 11.9894},
            {"storeId": 7150, "namn": "POWER Högsbo", "adress": "Lona Knapes gata 1", "postnummer": "42132", "stad": "Västra Frölunda", "region": "Västra Götaland", "lat": 57.6348, "lng": 11.9059},
            {"storeId": 7151, "namn": "POWER Bäckebol", "adress": "Transportgatan 19", "postnummer": "42246", "stad": "Hisings Backa", "region": "Västra Götaland", "lat": 57.7461, "lng": 11.9094},
            {"storeId": 7156, "namn": "POWER Borås", "adress": "Ålgårdsvägen 11", "postnummer": "50630", "stad": "Borås", "region": "Västra Götaland", "lat": 57.7210, "lng": 12.9401},
            {"storeId": 7125, "namn": "POWER Jönköping", "adress": "Solåsvägen 4A", "postnummer": "55303", "stad": "Jönköping", "region": "Jönköping", "lat": 57.7826, "lng": 14.1618},
            {"storeId": 7155, "namn": "POWER Skövde", "adress": "Jonstorpsgatan 3C", "postnummer": "54937", "stad": "Skövde", "region": "Västra Götaland", "lat": 58.3875, "lng": 13.8458},
            {"storeId": 7147, "namn": "POWER Helsingborg Väla", "adress": "Marknadsvägen 5", "postnummer": "25469", "stad": "Ödåkra", "region": "Skåne", "lat": 56.0776, "lng": 12.7441},
            {"storeId": 7170, "namn": "POWER Västerås", "adress": "Hallsta Gårdsgata 7", "postnummer": "72138", "stad": "Västerås", "region": "Västmanland", "lat": 59.6099, "lng": 16.5448},
            {"storeId": 7148, "namn": "POWER Lund", "adress": "Avtalsvägen 2", "postnummer": "22761", "stad": "Lund", "region": "Skåne", "lat": 55.7047, "lng": 13.2900},
            {"storeId": 7149, "namn": "POWER Kristianstad", "adress": "Fundamentgatan 1", "postnummer": "29161", "stad": "Kristianstad", "region": "Skåne", "lat": 56.0280, "lng": 14.1567},
            {"storeId": 7153, "namn": "POWER Linköping", "adress": "Björkgatan 4", "postnummer": "58252", "stad": "Linköping", "region": "Östergötland", "lat": 58.4108, "lng": 15.6214},
            {"storeId": 7154, "namn": "POWER Norrköping", "adress": "Koppargatan 30", "postnummer": "60223", "stad": "Norrköping", "region": "Östergötland", "lat": 58.5877, "lng": 16.1924},
            {"storeId": 7146, "namn": "POWER Malmö Svågertorp", "adress": "Nornegatan 8", "postnummer": "21586", "stad": "Malmö", "region": "Skåne", "lat": 55.5636, "lng": 12.9719},
            {"storeId": 7157, "namn": "POWER Uppsala", "adress": "Stångjärnsgatan 10", "postnummer": "75323", "stad": "Uppsala", "region": "Uppsala", "lat": 59.8586, "lng": 17.6389},
            {"storeId": 7158, "namn": "POWER Örebro", "adress": "Bettorpsgatan 4", "postnummer": "70369", "stad": "Örebro", "region": "Örebro", "lat": 59.2741, "lng": 15.2066},
            {"storeId": 7159, "namn": "POWER Sundsvall", "adress": "Norra Förmansvägen 18", "postnummer": "86341", "stad": "Sundsvall", "region": "Västernorrland", "lat": 62.3908, "lng": 17.3069},
            {"storeId": 7160, "namn": "POWER Gävle", "adress": "Ingenjörsgatan 2", "postnummer": "80293", "stad": "Gävle", "region": "Gävleborg", "lat": 60.6749, "lng": 17.1413},
            {"storeId": 7161, "namn": "POWER Stockholm Kungens Kurva", "adress": "Geometrivägen 1", "postnummer": "14175", "stad": "Kungens Kurva", "region": "Stockholm", "lat": 59.2465, "lng": 17.9414},
            {"storeId": 7162, "namn": "POWER Stockholm Barkarby", "adress": "Herrestavägen 20", "postnummer": "17738", "stad": "Järfälla", "region": "Stockholm", "lat": 59.4142, "lng": 17.8907}
        ];

        // HomePod produkter från plist
        const homepodProducts = [
            {productId: 1935540, title: "Apple Homepod (Andra Generationen), vit"},
            {productId: 3481365, title: "Apple Homepod Mini högtalare, midnatt"},
            {productId: 2193593, title: "Apple Homepod (Andra Generationen), Midnatt"},
            {productId: 2193594, title: "Apple Homepod (Andra Generationen), vit"},
            {productId: 2193596, title: "Apple Homepod Mini högtalare, vit"},
            {productId: 2193591, title: "Apple Homepod Mini högtalare, orange"},
            {productId: 2193592, title: "Apple Homepod Mini högtalare, gul"},
            {productId: 2193590, title: "Apple Homepod Mini Högtalaer, blå"},
            {productId: 1898801, title: "Apple Homepod Mini högtalare, vit"}
        ];

        let userLocation = null;
        let selectedStores = [];

        // Beräkna avstånd mellan två koordinater
        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371;
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // Hämta användarens position
        function getUserLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        
                        document.getElementById('location-status').textContent = 
                            `Position hittad - visar närliggande butiker`;
                        
                        displayStores();
                    },
                    (error) => {
                        console.log('Geolocation error:', error);
                        document.getElementById('location-status').textContent = 
                            'Kunde inte hitta position - visa alla butiker';
                        displayStores();
                    }
                );
            } else {
                document.getElementById('location-status').textContent = 
                    'Geolocation stöds inte - visa alla butiker';
                displayStores();
            }
        }

        // Visa butiker sorterade efter avstånd
        function displayStores() {
            const storeSelector = document.getElementById('store-selector');
            let sortedStores = [...stores];

            if (userLocation) {
                sortedStores = stores.map(store => ({
                    ...store,
                    distance: calculateDistance(userLocation.lat, userLocation.lng, store.lat, store.lng)
                })).sort((a, b) => a.distance - b.distance);
                
                // Välj automatiskt de 3 närmaste butikerna
                selectedStores = sortedStores.slice(0, 3).map(store => store.storeId);
            }

            storeSelector.innerHTML = '';
            
            sortedStores.forEach(store => {
                const storeCard = document.createElement('div');
                storeCard.className = `store-card ${selectedStores.includes(store.storeId) ? 'selected' : ''}`;
                storeCard.onclick = () => toggleStore(store.storeId);
                
                let distanceText = '';
                if (store.distance) {
                    distanceText = `<div class="distance">${store.distance.toFixed(1)} km bort</div>`;
                }
                
                storeCard.innerHTML = `
                    <div class="store-name">${store.namn}</div>
                    <div class="store-address">${store.adress}, ${store.stad}</div>
                    ${distanceText}
                `;
                
                storeSelector.appendChild(storeCard);
            });

            updateLoadButton();
        }

        // Växla val av butik
        function toggleStore(storeId) {
            const index = selectedStores.indexOf(storeId);
            if (index > -1) {
                selectedStores.splice(index, 1);
            } else {
                selectedStores.push(storeId);
            }
            
            displayStores();
        }

        // Uppdatera load-knappen
        function updateLoadButton() {
            const button = document.getElementById('load-button');
            button.disabled = selectedStores.length === 0;
            button.textContent = selectedStores.length === 0 ? 
                'Välj minst en butik' : 
                `Ladda lagerstatus (${selectedStores.length} butiker)`;
        }

                // Ladda lagerstatus (MODIFIERAD FÖR ATT ANROPA DITT PI API - KORRIGERAD)
        async function loadStockData() {
            console.log("--- loadStockData() called (anropar Pi API) ---");
            const loading = document.getElementById('loading');
            const errorDiv = document.getElementById('error');
            const resultsDiv = document.getElementById('results');
            
            loading.style.display = 'block';
            errorDiv.style.display = 'none';
            resultsDiv.innerHTML = '';

            if (selectedStores.length === 0) {
                console.warn("Inga butiker valda.");
                resultsDiv.innerHTML = `<div style="grid-column: 1/-1; text-align: center; color: white; padding: 50px;">
                                          <h3>Välj minst en butik</h3>
                                      </div>`;
                loading.style.display = 'none';
                return;
            }

            // Konstruera URL med query parametrar
            const productIdsParam = homepodProducts.map(p => p.productId).join(',');
            const storeIdsParam = selectedStores.join(',');

            // ---------------------------------------------------------------------------
            // VIKTIGT: Använd din ngrok HTTPS URL här!
            const baseApiUrl = 'https://2f12-31-208-30-3.ngrok-free.app/api/current_stock'; 
            // ---------------------------------------------------------------------------
            
            let queryParams = [];
            if (productIdsParam) queryParams.push(`product_ids=${productIdsParam}`);
            if (storeIdsParam) queryParams.push(`store_ids=${storeIdsParam}`);
            
            const fullApiUrl = `${baseApiUrl}${queryParams.length > 0 ? '?' : ''}${queryParams.join('&')}`;

            console.log("Anropar API via ngrok på Pi:n:", fullApiUrl);

            try {
                const response = await fetch(fullApiUrl); 
                console.log(`Svar från Pi API (via ngrok) status: ${response.status}`);

                if (!response.ok) {
                    const errorText = await response.text(); // Försök läsa felmeddelandet från servern
                    console.error(`API anrop misslyckades: ${response.status} ${response.statusText}`, errorText);
                    throw new Error(`API anrop misslyckades: ${response.status}. Svar från server: ${errorText.substring(0,150)}`); // Visa början av serversvaret
                }
                
                const apiData = await response.json(); 
                console.log("Data mottagen från Pi API:", apiData);
                
                const productsWithStock = [];
                const productMap = new Map();

                for (const item of apiData) {
                    // if (item.stockCount > 0) { // Filtrera här eller i displayResults
                        if (!productMap.has(item.productId)) {
                            const originalProduct = homepodProducts.find(p => p.productId == item.productId);
                            const title = originalProduct ? originalProduct.title : item.productTitle;

                            const newProductEntry = {
                                productId: item.productId,
                                title: title, 
                                stocks: []
                            };
                            productMap.set(item.productId, newProductEntry);
                            productsWithStock.push(newProductEntry);
                        }
                        
                        const storeDetails = stores.find(s => s.storeId == item.storeId);
                        const storeName = storeDetails ? storeDetails.namn : `Butik ${item.storeId}`;
                        const storeCity = storeDetails ? storeDetails.stad : '';

                        productMap.get(item.productId).stocks.push({
                            store: { namn: storeName, stad: storeCity, storeId: item.storeId }, 
                            stock: item.stockCount
                        });
                    // } 
                }
                
                console.log("Omvandlad productsWithStock:", productsWithStock);
                displayResults(productsWithStock);

            } catch (err) {
                console.error("Fel vid hämtning/bearbetning från Pi API:", err);
                errorDiv.textContent = `Kunde inte hämta lagerstatus: ${err.message}. Se webbläsarkonsolen för mer detaljer.`;
                errorDiv.style.display = 'block';
                resultsDiv.innerHTML = `<div style="grid-column: 1/-1; text-align: center; color: white; padding: 50px;">
                                          <h3>Ett fel uppstod</h3>
                                          <p>Kunde inte ladda lagerstatus. Försök igen senare.</p>
                                      </div>`;
            } finally {
                loading.style.display = 'none';
                console.log("--- loadStockData() (Pi API) finished ---");
            }
        }

        // Visa resultat
        function displayResults(products) {
            const results = document.getElementById('results');
            
            if (products.length === 0) {
                results.innerHTML = `
                    <div style="grid-column: 1/-1; text-align: center; color: white; padding: 50px;">
                        <h3>Inga HomePod produkter i lager</h3>
                        <p>Inga av de valda butikerna har HomePod produkter i lager just nu.</p>
                    </div>
                `;
                return;
            }

            results.innerHTML = '';
            
            products.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                
                const stockItems = product.stocks.map(stock => `
                    <div class="stock-item">
                        <div class="store-info">
                            <div class="store-name-small">${stock.store.namn}</div>
                            <div class="store-city">${stock.store.stad}</div>
                        </div>
                        <div class="stock-count">${stock.stock} st</div>
                    </div>
                `).join('');
                
                productCard.innerHTML = `
                    <div class="product-title">${product.title}</div>
                    <div class="stock-info">
                        ${stockItems}
                    </div>
                `;
                
                results.appendChild(productCard);
            });
        }

        // Starta appen
        getUserLocation();
    </script>
</body>
</html>