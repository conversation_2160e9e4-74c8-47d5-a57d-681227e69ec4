#!/usr/bin/env python3
"""
Diagnostikscript för att kontrollera Power Stock-databasen
"""

import sqlite3
import os
from datetime import datetime

DATABASE_NAME = 'power_stock.db'

def check_database_status():
    """Kontrollerar databasens status och innehåll"""
    
    if not os.path.exists(DATABASE_NAME):
        print(f"❌ Databasen '{DATABASE_NAME}' finns inte!")
        return False
    
    print(f"✅ Databasen '{DATABASE_NAME}' finns")
    print(f"📁 Filstorlek: {os.path.getsize(DATABASE_NAME)} bytes")
    
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()
        
        # Kontrollera tabeller
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"\n📋 Tabeller i databasen: {[table[0] for table in tables]}")
        
        # Kontrollera produkter
        cursor.execute("SELECT COUNT(*) FROM products")
        product_count = cursor.fetchone()[0]
        print(f"\n📦 Antal produkter totalt: {product_count}")
        
        cursor.execute("SELECT COUNT(*) FROM products WHERE active = 1")
        active_product_count = cursor.fetchone()[0]
        print(f"✅ Antal aktiva produkter: {active_product_count}")
        
        # Kontrollera lagerhistorik
        cursor.execute("SELECT COUNT(*) FROM stock_levels_history")
        history_count = cursor.fetchone()[0]
        print(f"\n📊 Antal lagerhistorik-poster: {history_count}")
        
        # Kontrollera senaste tidsstämplar
        cursor.execute("""
            SELECT timestamp, COUNT(*) as count 
            FROM stock_levels_history 
            GROUP BY DATE(timestamp) 
            ORDER BY timestamp DESC 
            LIMIT 5
        """)
        recent_dates = cursor.fetchall()
        print(f"\n🕒 Senaste lageruppdateringar per dag:")
        for date, count in recent_dates:
            print(f"   {date}: {count} poster")
        
        # Kontrollera senaste tidsstämpel
        cursor.execute("SELECT MAX(timestamp) FROM stock_levels_history")
        latest_timestamp = cursor.fetchone()[0]
        if latest_timestamp:
            print(f"\n⏰ Senaste lageruppdatering: {latest_timestamp}")
            
            # Konvertera till datetime för att visa hur länge sedan
            try:
                latest_dt = datetime.fromisoformat(latest_timestamp.replace('Z', '+00:00'))
                now = datetime.now()
                diff = now - latest_dt.replace(tzinfo=None)
                hours_ago = diff.total_seconds() / 3600
                print(f"   Det var {hours_ago:.1f} timmar sedan")
            except Exception as e:
                print(f"   (Kunde inte beräkna tid sedan: {e})")
        else:
            print(f"\n❌ Ingen lagerhistorik hittades!")
        
        # Visa exempel på lagerdata
        cursor.execute("""
            SELECT h.productId, p.title, h.storeId, h.stockCount, h.timestamp
            FROM stock_levels_history h
            JOIN products p ON h.productId = p.productId
            WHERE p.active = 1
            ORDER BY h.timestamp DESC
            LIMIT 5
        """)
        sample_data = cursor.fetchall()
        print(f"\n🔍 Exempel på senaste lagerdata:")
        for row in sample_data:
            product_id, title, store_id, stock, timestamp = row
            print(f"   {title[:30]:<30} | Butik {store_id} | {stock} st | {timestamp}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Fel vid databasanslutning: {e}")
        return False

def check_get_power_stock_process():
    """Kontrollerar om get_power_stock.py-processen körs"""
    import subprocess
    
    try:
        # Kontrollera om processen körs
        result = subprocess.run(['pgrep', '-f', 'get_power_stock.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"✅ get_power_stock.py körs (PID: {', '.join(pids)})")
            return True
        else:
            print(f"❌ get_power_stock.py körs INTE")
            return False
            
    except Exception as e:
        print(f"⚠️  Kunde inte kontrollera process: {e}")
        return False

def check_plist_files():
    """Kontrollerar om plist-filerna finns"""
    plist_dir = '/home/<USER>/power_scraper/'
    plist_files = [
        "iphone.plist", "ipad.plist", "mac.plist", "airpods.plist",
        "watch.plist", "appletv.plist", "homepod.plist", "tillbehor.plist"
    ]
    
    print(f"\n📁 Kontrollerar plist-filer i {plist_dir}:")
    
    if not os.path.exists(plist_dir):
        print(f"❌ Katalogen {plist_dir} finns inte!")
        return False
    
    all_exist = True
    for filename in plist_files:
        filepath = os.path.join(plist_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"   ✅ {filename} ({size} bytes)")
        else:
            print(f"   ❌ {filename} SAKNAS")
            all_exist = False
    
    return all_exist

if __name__ == "__main__":
    print("🔍 Power Stock Database Diagnostik")
    print("=" * 50)
    
    # Kontrollera databas
    db_ok = check_database_status()
    
    print("\n" + "=" * 50)
    
    # Kontrollera process
    process_ok = check_get_power_stock_process()
    
    print("\n" + "=" * 50)
    
    # Kontrollera plist-filer
    plist_ok = check_plist_files()
    
    print("\n" + "=" * 50)
    print("📋 SAMMANFATTNING:")
    print(f"   Databas: {'✅ OK' if db_ok else '❌ Problem'}")
    print(f"   Process: {'✅ Körs' if process_ok else '❌ Körs inte'}")
    print(f"   Plist-filer: {'✅ OK' if plist_ok else '❌ Problem'}")
    
    if not process_ok:
        print(f"\n💡 För att starta get_power_stock.py:")
        print(f"   cd {os.getcwd()}")
        print(f"   python3 get_power_stock.py")
