import json
import time
import sqlite3
import requests 
import plistlib 
import os 

# --- Konfiguration ---
DATABASE_NAME = 'power_stock.db'
PLIST_DIRECTORY = '/home/<USER>/power_scraper/' 
PLIST_FILES_AND_CATEGORIES = {
    "iphone.plist": "iPhone",
    "ipad.plist": "iPad",
    "mac.plist": "Mac",
    "airpods.plist": "AirPods",
    "watch.plist": "Watch",
    "appletv.plist": "Apple TV",
    "homepod.plist": "HomePod",
    "tillbehor.plist": "Tillbehör",
}
DELAY_BETWEEN_PRODUCTS = 2 # Kan sänkas lite för snabbare körning
REQUEST_TIMEOUT = 30 
HTTP_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36'
}
MAX_RETRIES_PER_PRODUCT = 2 
DELAY_BETWEEN_RETRIES = 5   
# ---------------------

def init_db_and_get_connection(db_name):
    """Initialiserar DB, tabeller och returnerar en anslutning."""
    conn = sqlite3.connect(db_name)
    cursor = conn.cursor()
    # ÄNDRAT: Lade till 'active' och justerade kolumner.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            productId TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            categoryName TEXT,
            ean TEXT,
            sku TEXT,
            price REAL,
            active INTEGER DEFAULT 0,
            last_checked DATETIME DEFAULT CURRENT_TIMESTAMP
        )''')
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_levels_history (
            log_id INTEGER PRIMARY KEY AUTOINCREMENT,
            productId TEXT,
            stockCount INTEGER,
            storeId TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (productId) REFERENCES products (productId)
        )''')
    # ÄNDRAT: Lade till index för prestanda om de inte redan finns
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_products_categoryName ON products (categoryName);")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_stock_history_lookup ON stock_levels_history (productId, storeId, log_id DESC);")
    conn.commit()
    print(f"Databas '{db_name}' initialiserad/verifierad.")
    return conn

def load_products_from_plists(directory, plist_config):
    all_products = {} # ÄNDRAT: Använder en dict för att undvika dubbletter
    print(f"Läser produktlistor från .plist-filer i katalogen: {directory}")
    for filename, category_name in plist_config.items():
        filepath = os.path.join(directory, filename)
        try:
            with open(filepath, 'rb') as fp: 
                plist_data = plistlib.load(fp)
                count = 0
                if isinstance(plist_data, list):
                    for item in plist_data:
                        if isinstance(item, dict) and 'productId' in item and 'title' in item:
                            product_id = str(item['productId'])
                            if product_id not in all_products:
                                all_products[product_id] = {
                                    "title_from_plist": item['title'],
                                    "category": category_name
                                }
                                count += 1
                    print(f"  - Läste {count} nya produkter från {filename} (Kategori: {category_name})")
                else:
                    print(f"  VARNING: Förväntade en lista i {filename}, men fick {type(plist_data)}")
        except FileNotFoundError:
            print(f"  FEL: Plist-filen {filepath} hittades inte.")
        except Exception as e:
            print(f"  FEL vid läsning av {filepath}: {e}")
            
    if not all_products:
        print("VARNING: Inga produkter laddades från .plist-filerna.")
    else:
        print(f"Totalt {len(all_products)} unika produkter laddade från alla .plist-filer.")
    return all_products

def fetch_product_data_via_requests(product_id):
    api_url = f"https://power.se/api/sparoute/x/p-{product_id}/"
    for attempt in range(MAX_RETRIES_PER_PRODUCT + 1):
        try:
            response = requests.get(api_url, headers=HTTP_HEADERS, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            data = response.json()
            # print(f"    Lyckades hämta data för {product_id} på försök {attempt + 1}.")
            return data
        except requests.exceptions.HTTPError as errh:
            print(f"  Http Error (försök {attempt + 1}): {errh}. Status: {response.status_code}")
            return {"error": "HTTPError", "message": str(errh), "status_code": response.status_code}
        except (requests.exceptions.RequestException, json.JSONDecodeError) as err:
            print(f"  Nätverks/JSON-fel (försök {attempt + 1}): {err}")
            if attempt < MAX_RETRIES_PER_PRODUCT:
                print(f"    Väntar {DELAY_BETWEEN_RETRIES} sekunder...")
                time.sleep(DELAY_BETWEEN_RETRIES)
            else:
                print(f"  Alla {MAX_RETRIES_PER_PRODUCT + 1} försök misslyckades.")
                return {"error": type(err).__name__, "message": str(err)}
    return {"error": "MaxRetriesReached", "message": "Max antal försök nåddes."}

# NYTT: Funktion för att deaktivera alla produkter
def deactivate_all_products(conn):
    """Markerar alla produkter som inaktiva. Returnerar antalet påverkade rader."""
    cursor = conn.cursor()
    cursor.execute("UPDATE products SET active = 0")
    conn.commit()
    print(f"\nMarkerade {cursor.rowcount} befintliga produkter som inaktiva i väntan på verifiering.")
    return cursor.rowcount

def parse_price(price_og_item):
    """Säkrare parsning av prissträng från OpenGraph-data."""
    if not price_og_item or not isinstance(price_og_item.get("Value"), str):
        return None
    price_str = price_og_item["Value"]
    print(f"  Hittade pris-sträng från API: '{price_str}'") # Bra för felsökning
    try:
        # --- NY, MER ROBUST LOGIK ---
        # 1. Byt ut eventuellt kommatecken mot punkt
        normalized_price_str = price_str.replace(',', '.')

        # 2. Konvertera till flyttal.
        #    Om Powers API returnerar "8490,0000" för 8490 kr, betyder det att
        #    det är en sträng som representerar priset, men med kommatecken.
        #    Vi antar att de sista fyra siffrorna är decimaler.
        #    Om vi bara byter komma mot punkt får vi "8490.0000".
        price_as_float = float(normalized_price_str)

        # Om vi inte behöver dela med 10000 (om float() ger rätt värde direkt):
        # price_from_api = price_as_float

        # Om vi ALLTID har 4 decimaler i strängen:
        # Vi kan helt enkelt avrunda till 2 decimaler.
        price_from_api = round(price_as_float, 2)
        # --- SLUT PÅ NY LOGIK ---

        return price_from_api
    except (ValueError, TypeError) as e:
        print(f"  VARNING: Kunde inte konvertera normaliserad prissträng '{normalized_price_str}' till float. Fel: {e}")
        return None

def update_product_and_log_history(conn, product_id, plist_info, api_data):
    """
    Uppdaterar en enskild produkt och loggar dess lagerhistorik.
    Detta är nu den centrala funktionen som hanterar datan för en produkt.
    """
    cursor = conn.cursor()

    # Steg 1: Extrahera och sanera data från API-svaret
    title_from_api = None
    if api_data.get("PwrMainModel", {}).get("SeoModel", {}).get("title"):
        full_title = api_data["PwrMainModel"]["SeoModel"]["title"]
        title_from_api = full_title[:-11].strip() if full_title.endswith(" - Power.se") else full_title.strip()

    ean_from_api = next((spec["Values"][0] for spec in api_data.get("Model", {}).get("MdmSpecifications", []) if spec.get("NameKey") == "Product.EAN" and spec.get("Values")), None)
    sku_from_api = next((spec["Values"][0] for spec in api_data.get("Model", {}).get("MdmSpecifications", []) if spec.get("NameKey") == "Product.ManufacturerSKU" and spec.get("Values")), None)
    price_og_item = next((item for item in api_data.get("PwrMainModel", {}).get("SeoModel", {}).get("openGraphItems", []) if item.get("Key") == "product:price:amount"), None)
    price_from_api = parse_price(price_og_item)

    # Steg 2: Bestäm slutgiltig titel och kategori
    final_title = plist_info["title_from_plist"] or title_from_api or f"Produkt {product_id}"
    final_category = plist_info["category"]

    # Steg 3: Uppdatera produkttabellen (UPSERT)
    # Endast om API-anropet var lyckat uppdateras ean, sku, price.
    try:
        cursor.execute("""
            SELECT ean, sku, price FROM products WHERE productId = ?
        """, (product_id,))
        existing_data = cursor.fetchone()

        # Använd befintlig data som fallback om ny data saknas
        final_ean = ean_from_api if ean_from_api is not None else (existing_data[0] if existing_data else None)
        final_sku = sku_from_api if sku_from_api is not None else (existing_data[1] if existing_data else None)
        final_price = price_from_api if price_from_api is not None else (existing_data[2] if existing_data else None)

        cursor.execute('''
            INSERT INTO products (productId, title, categoryName, ean, sku, price, active, last_checked) 
            VALUES (?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP)
            ON CONFLICT(productId) DO UPDATE SET
                title=excluded.title,
                categoryName=excluded.categoryName,
                ean=excluded.ean,
                sku=excluded.sku,
                price=excluded.price,
                active=1,
                last_checked=CURRENT_TIMESTAMP;
        ''', (product_id, final_title, final_category, final_ean, final_sku, final_price))
        # print(f"  - Produktinfo för '{final_title}' sparad/uppdaterad.")
    except sqlite3.Error as e:
        print(f"  - DB-FEL vid uppdatering av produkt {product_id}: {e}")
        return # Avbryt om vi inte kan spara produkten

    # Steg 4: Logga lagerhistorik
    product_store_stocks = api_data.get("Model", {}).get("ExtendedProduct", {}).get("ProductStoreStocks")
    if product_store_stocks and isinstance(product_store_stocks, dict):
        entries = []
        for store_id, stock_info in product_store_stocks.items():
            stock_count = stock_info.get("Stock", 0)
            entries.append((product_id, store_id, stock_count))
        
        cursor.executemany('INSERT INTO stock_levels_history (productId, storeId, stockCount) VALUES (?, ?, ?)', entries)
        print(f"  - Loggade lagerstatus för {len(entries)} butiker för '{final_title}'.")
    else:
        print(f"  - Ingen lagerdata att logga för '{final_title}'.")

    conn.commit()


if __name__ == "__main__":
    db_connection = init_db_and_get_connection(DATABASE_NAME)
    products_to_process = load_products_from_plists(PLIST_DIRECTORY, PLIST_FILES_AND_CATEGORIES)

    if not products_to_process:
        print("Inga produkter att bearbeta. Avslutar.")
    else:
        # Samla alla produkter som ska bearbetas
        successfully_processed_products = set()
        total_products = len(products_to_process)

        print(f"Börjar bearbeta {total_products} produkter utan att påverka befintliga data...")

        for i, (product_id, plist_info) in enumerate(products_to_process.items()):
            print(f"\n--- Bearbetar {i+1}/{total_products}: {plist_info['title_from_plist']} (ID: {product_id}) ---")

            api_data = fetch_product_data_via_requests(product_id)

            if api_data and not api_data.get("error"):
                update_product_and_log_history(db_connection, product_id, plist_info, api_data)
                successfully_processed_products.add(product_id)
                print(f"  ✅ Framgångsrikt bearbetad: {plist_info['title_from_plist']}")
            else:
                # API-anropet misslyckades. Logga felet men behåll befintlig data.
                error_msg = api_data.get('message', 'Okänt fel') if api_data else 'Inget svar från server'
                print(f"  ❌ FEL vid API-anrop för {product_id}. Info: {error_msg}. Behåller befintlig data.")

            print(f"    (Väntar {DELAY_BETWEEN_PRODUCTS} sekunder...)")
            time.sleep(DELAY_BETWEEN_PRODUCTS)

        # Nu deaktivera endast produkter som INTE fanns i plist-filerna
        print(f"\nDeaktiverar produkter som inte längre finns i plist-filerna...")
        cursor = db_connection.cursor()

        # Hämta alla produkter som finns i databasen
        cursor.execute("SELECT productId FROM products WHERE active = 1")
        existing_products = {row[0] for row in cursor.fetchall()}

        # Produkter som ska finnas (från plist-filerna)
        products_that_should_exist = set(products_to_process.keys())

        # Produkter som ska deaktiveras (finns i DB men inte i plist)
        products_to_deactivate = existing_products - products_that_should_exist

        if products_to_deactivate:
            placeholders = ','.join('?' * len(products_to_deactivate))
            cursor.execute(f"UPDATE products SET active = 0 WHERE productId IN ({placeholders})",
                          list(products_to_deactivate))
            db_connection.commit()
            print(f"  Deaktiverade {len(products_to_deactivate)} produkter som inte längre finns i plist-filerna.")
        else:
            print("  Inga produkter behövde deaktiveras.")

        print(f"\n📊 Sammanfattning:")
        print(f"  - Framgångsrikt bearbetade: {len(successfully_processed_products)}/{total_products}")
        print(f"  - Misslyckade API-anrop: {total_products - len(successfully_processed_products)}")
        print(f"  - Deaktiverade gamla produkter: {len(products_to_deactivate) if products_to_deactivate else 0}")

    db_connection.close()
    print(f"\n\n--- Datainsamling slutförd ---")