{"log": {"version": "1.2", "creator": {"name": "WebKit Web Inspector", "version": "1.0"}, "pages": [{"startedDateTime": "2025-05-23T20:18:40.383Z", "id": "page_0", "title": "http://************:8080/power_stock_checker.html", "pageTimings": {"onContentLoad": 2414.212708332343, "onLoad": 2413.495166678331}}], "entries": [{"pageref": "page_0", "startedDateTime": "2025-05-23T20:18:40.383Z", "time": 10.418088684673421, "request": {"method": "GET", "url": "http://************:8080/power_stock_checker.html", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}, {"name": "Accept-Language", "value": "sv-SE,sv;q=0.9"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://************:8080/"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Priority", "value": "u=0, i"}], "queryString": [], "headersSize": 444, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Cache-Control", "value": "max-age=3600"}, {"name": "Content-Type", "value": "text/html; charset=UTF-8"}, {"name": "ETag", "value": "W/\"121390389-20717-2025-05-22T22:33:06.180Z\""}, {"name": "Last-Modified", "value": "Thu, 22 May 2025 22:33:06 GMT"}, {"name": "Accept-Ranges", "value": "bytes"}, {"name": "Date", "value": "Fri, 23 May 2025 20:18:40 GMT"}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Content-Length", "value": "20717"}, {"name": "Connection", "value": "keep-alive"}], "content": {"size": 20717, "compression": 0, "mimeType": "text/html", "text": "<!DOCTYPE html>\n<html lang=\"sv\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Power Lagerstatus</title>\n    <style>\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            min-height: 100vh;\n            padding: 20px;\n        }\n\n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n        }\n\n        .header {\n            text-align: center;\n            color: white;\n            margin-bottom: 30px;\n        }\n\n        .header h1 {\n            font-size: 2.5rem;\n            font-weight: 700;\n            margin-bottom: 10px;\n        }\n\n        .header p {\n            opacity: 0.9;\n            font-size: 1.1rem;\n        }\n\n        .controls {\n            background: white;\n            border-radius: 15px;\n            padding: 25px;\n            margin-bottom: 30px;\n            box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n        }\n\n        .location-info {\n            display: flex;\n            align-items: center;\n            gap: 15px;\n            margin-bottom: 20px;\n            padding: 15px;\n            background: #f8f9fa;\n            border-radius: 10px;\n        }\n\n        .location-icon {\n            width: 40px;\n            height: 40px;\n            background: #667eea;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: white;\n            font-size: 18px;\n        }\n\n        .store-selector {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n            gap: 15px;\n            margin-bottom: 20px;\n        }\n\n        .store-card {\n            padding: 15px;\n            border: 2px solid #e9ecef;\n            border-radius: 10px;\n            cursor: pointer;\n            transition: all 0.3s ease;\n            background: white;\n        }\n\n        .store-card:hover {\n            border-color: #667eea;\n            transform: translateY(-2px);\n            box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n        }\n\n        .store-card.selected {\n            border-color: #667eea;\n            background: #f8f9ff;\n        }\n\n        .store-name {\n            font-weight: 600;\n            color: #2c3e50;\n            margin-bottom: 5px;\n        }\n\n        .store-address {\n            color: #6c757d;\n            font-size: 0.9rem;\n        }\n\n        .distance {\n            color: #667eea;\n            font-size: 0.8rem;\n            font-weight: 500;\n            margin-top: 5px;\n        }\n\n        .load-button {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            border: none;\n            padding: 15px 30px;\n            border-radius: 10px;\n            font-size: 1.1rem;\n            font-weight: 600;\n            cursor: pointer;\n            transition: transform 0.2s ease;\n            width: 100%;\n        }\n\n        .load-button:hover {\n            transform: translateY(-2px);\n        }\n\n        .load-button:disabled {\n            opacity: 0.6;\n            cursor: not-allowed;\n            transform: none;\n        }\n\n        .results {\n            display: grid;\n            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n            gap: 20px;\n        }\n\n        .product-card {\n            background: white;\n            border-radius: 15px;\n            padding: 20px;\n            box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n            transition: transform 0.3s ease;\n        }\n\n        .product-card:hover {\n            transform: translateY(-5px);\n        }\n\n        .product-title {\n            font-size: 1.1rem;\n            font-weight: 600;\n            color: #2c3e50;\n            margin-bottom: 15px;\n            line-height: 1.4;\n        }\n\n        .stock-info {\n            display: grid;\n            gap: 10px;\n        }\n\n        .stock-item {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 10px 15px;\n            background: #f8f9fa;\n            border-radius: 8px;\n        }\n\n        .store-info {\n            flex: 1;\n        }\n\n        .store-name-small {\n            font-weight: 500;\n            color: #2c3e50;\n        }\n\n        .store-city {\n            font-size: 0.85rem;\n            color: #6c757d;\n        }\n\n        .stock-count {\n            background: #28a745;\n            color: white;\n            padding: 5px 12px;\n            border-radius: 20px;\n            font-weight: 600;\n            font-size: 0.9rem;\n        }\n\n        .loading {\n            text-align: center;\n            padding: 50px;\n            color: white;\n            font-size: 1.2rem;\n        }\n\n        .spinner {\n            width: 40px;\n            height: 40px;\n            border: 4px solid rgba(255,255,255,0.3);\n            border-top: 4px solid white;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin: 0 auto 20px;\n        }\n\n        @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n        }\n\n        .error {\n            background: #dc3545;\n            color: white;\n            padding: 15px;\n            border-radius: 10px;\n            margin: 20px 0;\n            text-align: center;\n        }\n\n        @media (max-width: 768px) {\n            .header h1 {\n                font-size: 2rem;\n            }\n            \n            .store-selector {\n                grid-template-columns: 1fr;\n            }\n            \n            .results {\n                grid-template-columns: 1fr;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>🏪 Power Lagerstatus</h1>\n            <p>Intern portal för lagerkoll - HomePod test</p>\n        </div>\n\n        <div class=\"controls\">\n            <div class=\"location-info\">\n                <div class=\"location-icon\">📍</div>\n                <div>\n                    <div id=\"location-status\">Söker din position...</div>\n                    <div style=\"font-size: 0.9rem; color: #6c757d;\">Välj dina butiker nedan</div>\n                </div>\n            </div>\n\n            <div class=\"store-selector\" id=\"store-selector\">\n                <!-- Butiker läggs till här dynamiskt -->\n            </div>\n\n            <button class=\"load-button\" id=\"load-button\" onclick=\"loadStockData()\">\n                Ladda lagerstatus\n            </button>\n        </div>\n\n        <div id=\"loading\" class=\"loading\" style=\"display: none;\">\n            <div class=\"spinner\"></div>\n            Hämtar lagerstatus...\n        </div>\n\n        <div id=\"error\" class=\"error\" style=\"display: none;\"></div>\n\n        <div id=\"results\" class=\"results\"></div>\n    </div>\n\n    <script>\n        // Butiker från JSON-filen\n        const stores = [\n            {\"storeId\": 7152, \"namn\": \"POWER Torpavallen\", \"adress\": \"Torpavallsgatan 4D\", \"postnummer\": \"41673\", \"stad\": \"Göteborg\", \"region\": \"Västra Götaland\", \"lat\": 57.6792, \"lng\": 11.9894},\n            {\"storeId\": 7150, \"namn\": \"POWER Högsbo\", \"adress\": \"Lona Knapes gata 1\", \"postnummer\": \"42132\", \"stad\": \"Västra Frölunda\", \"region\": \"Västra Götaland\", \"lat\": 57.6348, \"lng\": 11.9059},\n            {\"storeId\": 7151, \"namn\": \"POWER Bäckebol\", \"adress\": \"Transportgatan 19\", \"postnummer\": \"42246\", \"stad\": \"Hisings Backa\", \"region\": \"Västra Götaland\", \"lat\": 57.7461, \"lng\": 11.9094},\n            {\"storeId\": 7156, \"namn\": \"POWER Borås\", \"adress\": \"Ålgårdsvägen 11\", \"postnummer\": \"50630\", \"stad\": \"Borås\", \"region\": \"Västra Götaland\", \"lat\": 57.7210, \"lng\": 12.9401},\n            {\"storeId\": 7125, \"namn\": \"POWER Jönköping\", \"adress\": \"Solåsvägen 4A\", \"postnummer\": \"55303\", \"stad\": \"Jönköping\", \"region\": \"Jönköping\", \"lat\": 57.7826, \"lng\": 14.1618},\n            {\"storeId\": 7155, \"namn\": \"POWER Skövde\", \"adress\": \"Jonstorpsgatan 3C\", \"postnummer\": \"54937\", \"stad\": \"Skövde\", \"region\": \"Västra Götaland\", \"lat\": 58.3875, \"lng\": 13.8458},\n            {\"storeId\": 7147, \"namn\": \"POWER Helsingborg Väla\", \"adress\": \"Marknadsvägen 5\", \"postnummer\": \"25469\", \"stad\": \"Ödåkra\", \"region\": \"Skåne\", \"lat\": 56.0776, \"lng\": 12.7441},\n            {\"storeId\": 7170, \"namn\": \"POWER Västerås\", \"adress\": \"Hallsta Gårdsgata 7\", \"postnummer\": \"72138\", \"stad\": \"Västerås\", \"region\": \"Västmanland\", \"lat\": 59.6099, \"lng\": 16.5448},\n            {\"storeId\": 7148, \"namn\": \"POWER Lund\", \"adress\": \"Avtalsvägen 2\", \"postnummer\": \"22761\", \"stad\": \"Lund\", \"region\": \"Skåne\", \"lat\": 55.7047, \"lng\": 13.2900},\n            {\"storeId\": 7149, \"namn\": \"POWER Kristianstad\", \"adress\": \"Fundamentgatan 1\", \"postnummer\": \"29161\", \"stad\": \"Kristianstad\", \"region\": \"Skåne\", \"lat\": 56.0280, \"lng\": 14.1567},\n            {\"storeId\": 7153, \"namn\": \"POWER Linköping\", \"adress\": \"Björkgatan 4\", \"postnummer\": \"58252\", \"stad\": \"Linköping\", \"region\": \"Östergötland\", \"lat\": 58.4108, \"lng\": 15.6214},\n            {\"storeId\": 7154, \"namn\": \"POWER Norrköping\", \"adress\": \"Koppargatan 30\", \"postnummer\": \"60223\", \"stad\": \"Norrköping\", \"region\": \"Östergötland\", \"lat\": 58.5877, \"lng\": 16.1924},\n            {\"storeId\": 7146, \"namn\": \"POWER Malmö Svågertorp\", \"adress\": \"Nornegatan 8\", \"postnummer\": \"21586\", \"stad\": \"Malmö\", \"region\": \"Skåne\", \"lat\": 55.5636, \"lng\": 12.9719},\n            {\"storeId\": 7157, \"namn\": \"POWER Uppsala\", \"adress\": \"Stångjärnsgatan 10\", \"postnummer\": \"75323\", \"stad\": \"Uppsala\", \"region\": \"Uppsala\", \"lat\": 59.8586, \"lng\": 17.6389},\n            {\"storeId\": 7158, \"namn\": \"POWER Örebro\", \"adress\": \"Bettorpsgatan 4\", \"postnummer\": \"70369\", \"stad\": \"Örebro\", \"region\": \"Örebro\", \"lat\": 59.2741, \"lng\": 15.2066},\n            {\"storeId\": 7159, \"namn\": \"POWER Sundsvall\", \"adress\": \"Norra Förmansvägen 18\", \"postnummer\": \"86341\", \"stad\": \"Sundsvall\", \"region\": \"Västernorrland\", \"lat\": 62.3908, \"lng\": 17.3069},\n            {\"storeId\": 7160, \"namn\": \"POWER Gävle\", \"adress\": \"Ingenjörsgatan 2\", \"postnummer\": \"80293\", \"stad\": \"Gävle\", \"region\": \"Gävleborg\", \"lat\": 60.6749, \"lng\": 17.1413},\n            {\"storeId\": 7161, \"namn\": \"POWER Stockholm Kungens Kurva\", \"adress\": \"Geometrivägen 1\", \"postnummer\": \"14175\", \"stad\": \"Kungens Kurva\", \"region\": \"Stockholm\", \"lat\": 59.2465, \"lng\": 17.9414},\n            {\"storeId\": 7162, \"namn\": \"POWER Stockholm Barkarby\", \"adress\": \"Herrestavägen 20\", \"postnummer\": \"17738\", \"stad\": \"Järfälla\", \"region\": \"Stockholm\", \"lat\": 59.4142, \"lng\": 17.8907}\n        ];\n\n        // HomePod produkter från plist\n        const homepodProducts = [\n            {productId: 1935540, title: \"Apple Homepod (Andra Generationen), vit\"},\n            {productId: 3481365, title: \"Apple Homepod Mini högtalare, midnatt\"},\n            {productId: 2193593, title: \"Apple Homepod (Andra Generationen), Midnatt\"},\n            {productId: 2193594, title: \"Apple Homepod (Andra Generationen), vit\"},\n            {productId: 2193596, title: \"Apple Homepod Mini högtalare, vit\"},\n            {productId: 2193591, title: \"Apple Homepod Mini högtalare, orange\"},\n            {productId: 2193592, title: \"Apple Homepod Mini högtalare, gul\"},\n            {productId: 2193590, title: \"Apple Homepod Mini Högtalaer, blå\"},\n            {productId: 1898801, title: \"Apple Homepod Mini högtalare, vit\"}\n        ];\n\n        let userLocation = null;\n        let selectedStores = [];\n\n        // Beräkna avstånd mellan två koordinater\n        function calculateDistance(lat1, lng1, lat2, lng2) {\n            const R = 6371;\n            const dLat = (lat2 - lat1) * Math.PI / 180;\n            const dLng = (lng2 - lng1) * Math.PI / 180;\n            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +\n                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *\n                    Math.sin(dLng/2) * Math.sin(dLng/2);\n            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n            return R * c;\n        }\n\n        // Hämta användarens position\n        function getUserLocation() {\n            if (navigator.geolocation) {\n                navigator.geolocation.getCurrentPosition(\n                    (position) => {\n                        userLocation = {\n                            lat: position.coords.latitude,\n                            lng: position.coords.longitude\n                        };\n                        \n                        document.getElementById('location-status').textContent = \n                            `Position hittad - visar närliggande butiker`;\n                        \n                        displayStores();\n                    },\n                    (error) => {\n                        console.log('Geolocation error:', error);\n                        document.getElementById('location-status').textContent = \n                            'Kunde inte hitta position - visa alla butiker';\n                        displayStores();\n                    }\n                );\n            } else {\n                document.getElementById('location-status').textContent = \n                    'Geolocation stöds inte - visa alla butiker';\n                displayStores();\n            }\n        }\n\n        // Visa butiker sorterade efter avstånd\n        function displayStores() {\n            const storeSelector = document.getElementById('store-selector');\n            let sortedStores = [...stores];\n\n            if (userLocation) {\n                sortedStores = stores.map(store => ({\n                    ...store,\n                    distance: calculateDistance(userLocation.lat, userLocation.lng, store.lat, store.lng)\n                })).sort((a, b) => a.distance - b.distance);\n                \n                // Välj automatiskt de 3 närmaste butikerna\n                selectedStores = sortedStores.slice(0, 3).map(store => store.storeId);\n            }\n\n            storeSelector.innerHTML = '';\n            \n            sortedStores.forEach(store => {\n                const storeCard = document.createElement('div');\n                storeCard.className = `store-card ${selectedStores.includes(store.storeId) ? 'selected' : ''}`;\n                storeCard.onclick = () => toggleStore(store.storeId);\n                \n                let distanceText = '';\n                if (store.distance) {\n                    distanceText = `<div class=\"distance\">${store.distance.toFixed(1)} km bort</div>`;\n                }\n                \n                storeCard.innerHTML = `\n                    <div class=\"store-name\">${store.namn}</div>\n                    <div class=\"store-address\">${store.adress}, ${store.stad}</div>\n                    ${distanceText}\n                `;\n                \n                storeSelector.appendChild(storeCard);\n            });\n\n            updateLoadButton();\n        }\n\n        // Växla val av butik\n        function toggleStore(storeId) {\n            const index = selectedStores.indexOf(storeId);\n            if (index > -1) {\n                selectedStores.splice(index, 1);\n            } else {\n                selectedStores.push(storeId);\n            }\n            \n            displayStores();\n        }\n\n        // Uppdatera load-knappen\n        function updateLoadButton() {\n            const button = document.getElementById('load-button');\n            button.disabled = selectedStores.length === 0;\n            button.textContent = selectedStores.length === 0 ? \n                'Välj minst en butik' : \n                `Ladda lagerstatus (${selectedStores.length} butiker)`;\n        }\n\n        // Ladda lagerstatus\n        async function loadStockData() {\n            const loading = document.getElementById('loading');\n            const error = document.getElementById('error');\n            const results = document.getElementById('results');\n            \n            loading.style.display = 'block';\n            error.style.display = 'none';\n            results.innerHTML = '';\n\n            const productsWithStock = [];\n\n            try {\n                for (const product of homepodProducts) {\n                    try {\n                        // const response = await fetch(`https://power.se/api/sparoute/x/p-${product.productId}/`);\n                      // test med CORS-proxy\n                      //const response = await fetch(`https://cors-anywhere.herokuapp.com/https://power.se/api/sparoute/x/p-${product.productId}/`);\n\t\t\t\t\t\t// Du kan behöva besöka https://cors-anywhere.herokuapp.com/ först och klicka på knappen för att aktivera tillfällig åtkomst.\n                        const response = await fetch(`http://localhost:3000/api/p-${product.productId}/`);\n                        const data = await response.json();\n                        \n                        if (data.Model && data.Model.ExtendedProduct && data.Model.ExtendedProduct.ProductStoreStocks) {\n                            const stocks = data.Model.ExtendedProduct.ProductStoreStocks;\n                            const relevantStocks = [];\n                            \n                            selectedStores.forEach(storeId => {\n                                if (stocks[storeId] && stocks[storeId].Stock > 0) {\n                                    const store = stores.find(s => s.storeId === storeId);\n                                    if (store) {\n                                        relevantStocks.push({\n                                            store: store,\n                                            stock: stocks[storeId].Stock\n                                        });\n                                    }\n                                }\n                            });\n                            \n                            if (relevantStocks.length > 0) {\n                                productsWithStock.push({\n                                    ...product,\n                                    stocks: relevantStocks\n                                });\n                            }\n                        }\n                        \n                        // Små delay för att inte överbelasta API:et\n                        await new Promise(resolve => setTimeout(resolve, 100));\n                        \n                    } catch (err) {\n                        console.error(`Error fetching data for product ${product.productId}:`, err);\n                    }\n                }\n\n                displayResults(productsWithStock);\n                \n            } catch (err) {\n                error.textContent = 'Ett fel uppstod vid hämtning av lagerstatus: ' + err.message;\n                error.style.display = 'block';\n            } finally {\n                loading.style.display = 'none';\n            }\n        }\n\n        // Visa resultat\n        function displayResults(products) {\n            const results = document.getElementById('results');\n            \n            if (products.length === 0) {\n                results.innerHTML = `\n                    <div style=\"grid-column: 1/-1; text-align: center; color: white; padding: 50px;\">\n                        <h3>Inga HomePod produkter i lager</h3>\n                        <p>Inga av de valda butikerna har HomePod produkter i lager just nu.</p>\n                    </div>\n                `;\n                return;\n            }\n\n            results.innerHTML = '';\n            \n            products.forEach(product => {\n                const productCard = document.createElement('div');\n                productCard.className = 'product-card';\n                \n                const stockItems = product.stocks.map(stock => `\n                    <div class=\"stock-item\">\n                        <div class=\"store-info\">\n                            <div class=\"store-name-small\">${stock.store.namn}</div>\n                            <div class=\"store-city\">${stock.store.stad}</div>\n                        </div>\n                        <div class=\"stock-count\">${stock.stock} st</div>\n                    </div>\n                `).join('');\n                \n                productCard.innerHTML = `\n                    <div class=\"product-title\">${product.title}</div>\n                    <div class=\"stock-info\">\n                        ${stockItems}\n                    </div>\n                `;\n                \n                results.appendChild(productCard);\n            });\n        }\n\n        // Starta appen\n        getUserLocation();\n    </script>\n</body>\n</html>"}, "redirectURL": "", "headersSize": 315, "bodySize": 20717, "_transferSize": 21032}, "cache": {}, "timings": {"blocked": 1.1570485221454874, "dns": 4.16621332988143e-05, "connect": 1.0002492781495675, "ssl": -1, "send": 1.2943744659423828, "wait": 6.960195241845213, "receive": 0.006137852324172854}, "serverIPAddress": "************", "_serverPort": 8080, "connection": "1", "_fetchType": "Network Load", "_priority": "high"}]}}