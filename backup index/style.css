 <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; font-weight: 700; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1rem; }
        
        .filters-container { background: rgba(255,255,255,0.9); border-radius: 15px; padding: 20px; margin-bottom: 20px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); }
        .filter-group { margin-bottom: 15px; }
        .filter-group:last-child { margin-bottom: 0; }
        .filter-group h3 { font-size: 1.1rem; color: #555; margin-bottom: 10px; border-bottom: 1px solid #eee; padding-bottom: 5px; }
        
        .filter-buttons button, #category-buttons button, #show-all-toggle { 
            padding: 8px 12px; margin: 5px 3px; border-radius: 8px; 
            border: 1px solid #ddd; background-color: #f8f9fa; 
            cursor: pointer; transition: all 0.2s ease;
            font-size: 0.9rem;
        }
        .filter-buttons button.active, #category-buttons button.active, #show-all-toggle.active { 
            background-color: #667eea; color: white; border-color: #5a6fcf;
            font-weight: bold; /* transform: translateY(-1px); */ box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .filter-buttons button:hover:not(.active), 
        #category-buttons button:hover:not(.active), 
        #show-all-toggle:hover:not(.active) { background-color: #e9ecef; }

        .controls { background: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        /* Borttagen .location-info då den integreras i knapparna eller statusdiv */
        #location-status-text { font-weight: 500; margin-bottom:15px; display:block; text-align: center; color: #555;}


        .store-selector { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .store-card { padding: 15px; border: 2px solid #e9ecef; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; background: white; }
        .store-card:hover { border-color: #667eea; transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .store-card.selected { border-color: #667eea; background: #f0f5ff; font-weight: bold; }
        .store-name { font-weight: 600; color: #2c3e50; margin-bottom: 5px; }
        .store-address { color: #6c757d; font-size: 0.9rem; }
        .distance { color: #667eea; font-size: 0.8rem; font-weight: 500; margin-top: 5px; }

        .load-button { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 15px 30px; border-radius: 10px; font-size: 1.1rem; font-weight: 600; cursor: pointer; transition: transform 0.2s ease; width: 100%; margin-top:10px;}
        .load-button:hover { transform: translateY(-2px); }
        .load-button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; background: #ccc; }

        .results { display: grid; grid-template-columns: repeat(auto-fill, minmax(330px, 1fr)); gap: 20px; }
        .product-card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 8px 25px rgba(0,0,0,0.08); transition: all 0.3s ease; }
        .product-card:hover { transform: translateY(-5px); }
        .product-card.out-of-stock-card { opacity: 0.65; }

        .product-title { font-size: 1.1rem; font-weight: 600; color: #2c3e50; margin-bottom: 5px; line-height: 1.4; }
        .product-details { font-size:0.8em; color:#444; margin-bottom:10px; }
        .stock-info { display: grid; gap: 10px; margin-top:10px; }
        .stock-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; background: #f8f9fa; border-radius: 8px; }
        .stock-item.out-of-stock-item { /* Ingen specifik stil här om kortet redan är utgråat */ }
        .store-info { flex: 1; }
        .store-name-small { font-weight: 500; color: #2c3e50; }
        .store-city { font-size: 0.85rem; color: #6c757d; }
        .stock-count { background: #28a745; color: white; padding: 5px 12px; border-radius: 20px; font-weight: 600; font-size: 0.9rem; }
        .stock-count.low { background: #ffc107; color: #333; } 
        .stock-count.empty { background: #adb5bd; color: #fff; } 

        .loading { text-align: center; padding: 50px; color: white; font-size: 1.2rem; }
        .spinner { width: 40px; height: 40px; border: 4px solid rgba(255,255,255,0.3); border-top: 4px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .error { background: #dc3545; color: white; padding: 15px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .footer-credit { text-align: center; color: rgba(255,255,255,0.7); margin-top: 40px; font-size: 0.9rem; }
        @media (max-width: 768px) { .header h1 { font-size: 2rem; } .store-selector, .results { grid-template-columns: 1fr; } }
    </style>