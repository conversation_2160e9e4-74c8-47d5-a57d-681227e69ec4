<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power Lagerstatus</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Power Lagerstatus</h1>
            <p>Intern portal för lagerkoll</p>
        </div>

        <div class="filters-container">
            <div class="filter-group">
                <h3>Välj Produktkategori:</h3>
                <div id="category-buttons" style="display: flex; flex-wrap: wrap; justify-content: center;">
                    <!-- Kategoriknappar genereras här av script.js -->
                </div>
            </div>
            <div class="filter-group">
                <h3>Välj Butiker:</h3>
                <div class="filter-buttons" style="text-align: center; margin-bottom: 5px;">
                    <button id="btn-closest" onclick="setStoreFilter('closest')">Min närmaste</button>
                    <button id="btn-nearby" onclick="setStoreFilter('nearby')">Närliggande (50km)</button>
                    <button id="btn-all_stores" onclick="setStoreFilter('all_stores')">Alla butiker</button>
                    <button id="btn-user_selected" onclick="setStoreFilter('user_selected')">Anpassat urval</button>
                </div>
            </div>
            <div class="filter-group" style="border-top: 1px solid #eee; padding-top:10px;">
                <h3>Lagerfilter:</h3>
                <div id="stock-status-filters" style="text-align: center; margin: 10px 0;">
                    <button id="btn-stock-in_stock" onclick="setStockStatusFilter('in_stock')" class="active">Endast i lager</button>
                    <button id="btn-stock-all" onclick="setStockStatusFilter('all')">Alla produkter</button>
                    <button id="btn-stock-out_of_stock" onclick="setStockStatusFilter('out_of_stock')">Endast slut i lager</button>
                </div>

                <h3>Visningsläge:</h3>
                <div style="text-align: center; margin: 10px 0;">
                    <button id="view-mode-toggle" onclick="toggleDisplayMode()">Kompakt vy</button>
                </div>
            </div>
        </div>

        <div class="controls">
            <div id="location-status-text" style="text-align:center; margin-bottom:15px; color: #555; font-weight:500;">Söker din position...</div>

            <div class="store-selector" id="store-selector" style="display:none;">
                <!-- Butiker läggs till här dynamiskt av script.js -->
            </div>

            <button class="load-button" id="load-button" onclick="loadStockData()">
                Hämta lagerstatus
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div> Hämtar lagerstatus...
        </div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="results" class="results">
            <!-- Produktkort genereras här av script.js -->
        </div>
        
        <div id="summary-section" class="filters-container" style="margin-top: 30px; display: none;">
            <div class="filter-group">
                <h3>Summering för aktuellt urval</h3>
                <div id="summary-content">
                    <!-- Summeringsinformation genereras här av script.js -->
                </div>
            </div>
        </div>
        
        <div class="footer-credit">
            <p>Skapad av Carl Rytterfalk</p>
        </div>
    </div>

    <script src="script.js?v=2.0.0"></script> <!-- Uppdaterad version med nya funktioner -->
</body>
</html>