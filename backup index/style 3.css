/* style.css */

* { 
    margin: 0; 
    padding: 0; 
    box-sizing: border-box; 
}

body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
    min-height: 100vh; 
    padding: 20px; 
    color: #333; 
}

.container { 
    max-width: 1200px; 
    margin: 0 auto; 
}

.header { 
    text-align: center; 
    color: white; 
    margin-bottom: 30px; 
}

.header h1 { 
    font-size: 2.5rem; 
    font-weight: 700; 
    margin-bottom: 10px; 
}

.header p { 
    opacity: 0.9; 
    font-size: 1.1rem; 
}
        
.filters-container { 
    background: rgba(255,255,255,0.9); 
    border-radius: 15px; 
    padding: 20px; 
    margin-bottom: 20px; 
    box-shadow: 0 5px 20px rgba(0,0,0,0.1); 
}

.filter-group { 
    margin-bottom: 15px; 
}

.filter-group:last-child { /* Ta bort marginal på sista gruppen i en container */
    margin-bottom: 0; 
}

.filter-group h3 { 
    font-size: 1.1rem; 
    color: #555; 
    margin-bottom: 10px; 
    border-bottom: 1px solid #eee; 
    padding-bottom: 5px; 
}
        
.filter-buttons button, 
#category-buttons button, 
#show-all-toggle { 
    padding: 8px 12px; 
    margin: 5px 3px; 
    border-radius: 8px; 
    border: 1px solid #ddd; 
    background-color: #f8f9fa; 
    cursor: pointer; 
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.filter-buttons button.active, 
#category-buttons button.active, 
#show-all-toggle.active { 
    background-color: #667eea; 
    color: white; 
    border-color: #5a6fcf;
    font-weight: bold; 
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.filter-buttons button:hover:not(.active), 
#category-buttons button:hover:not(.active), 
#show-all-toggle:hover:not(.active) { 
    background-color: #e9ecef; 
}

.controls { 
    background: white; 
    border-radius: 15px; 
    padding: 25px; 
    margin-bottom: 30px; 
    box-shadow: 0 10px 30px rgba(0,0,0,0.1); 
}

#location-status-text { 
    font-weight: 500; 
    margin-bottom:15px; 
    display:block; 
    text-align: center; 
    color: #555;
    min-height: 1.2em; /* För att undvika hopp när texten ändras */
}

.store-selector { 
    display: grid; 
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
    gap: 15px; 
    margin-bottom: 20px; 
}

.store-card { 
    padding: 15px; 
    border: 2px solid #e9ecef; 
    border-radius: 10px; 
    cursor: pointer; 
    transition: all 0.3s ease; 
    background: white; 
}

.store-card:hover { 
    border-color: #667eea; 
    transform: translateY(-2px); 
    box-shadow: 0 5px 15px rgba(0,0,0,0.08); 
}

.store-card.selected { 
    border-color: #667eea; 
    background: #f0f5ff; 
    font-weight: bold; 
}

.store-name { 
    font-weight: 600; 
    color: #2c3e50; 
    margin-bottom: 5px; 
}

.store-address { 
    color: #6c757d; 
    font-size: 0.9rem; 
}

.distance { 
    color: #667eea; 
    font-size: 0.8rem; 
    font-weight: 500; 
    margin-top: 5px; 
}

.load-button { 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
    color: white; 
    border: none; 
    padding: 15px 30px; 
    border-radius: 10px; 
    font-size: 1.1rem; 
    font-weight: 600; 
    cursor: pointer; 
    transition: transform 0.2s ease; 
    width: 100%; 
    margin-top:10px;
}

.load-button:hover { 
    transform: translateY(-2px); 
}

.load-button:disabled { 
    opacity: 0.6; 
    cursor: not-allowed; 
    transform: none; 
    background: #ccc; 
}

.results { 
    display: grid; 
    grid-template-columns: repeat(auto-fill, minmax(330px, 1fr)); 
    gap: 20px; 
}

.results h2 { /* Stil för kategorirubriker i resultatlistan */
    grid-column: 1 / -1; 
    color: white; 
    margin-top: 30px; 
    margin-bottom: 15px; 
    text-align: left; 
    padding-bottom: 5px; 
    border-bottom: 2px solid rgba(255,255,255,0.5); 
    font-size: 1.6rem;
}
.results h3 { /* Stil för sub-kategorirubriker */
    grid-column: 1 / -1;
    color: rgba(255,255,255,0.85);
    margin-top: 15px;
    margin-bottom: 8px;
    text-align: left;
    padding-left: 10px; /* Lite indrag för subrubrik */
    font-size: 1.2rem;
    font-weight: 500;
}


.product-card { 
    background: white; 
    border-radius: 15px; 
    padding: 20px; 
    box-shadow: 0 8px 25px rgba(0,0,0,0.08); 
    transition: all 0.3s ease; 
}

.product-card:hover { 
    transform: translateY(-5px); 
}

.product-card.out-of-stock-card { 
    opacity: 0.65; 
}

.product-title { 
    font-size: 1.1rem; 
    font-weight: 600; 
    color: #2c3e50; 
    margin-bottom: 5px; 
    line-height: 1.4; 
}

.product-details { 
    font-size:0.8em; 
    color:#444; 
    margin-bottom:10px; 
    line-height: 1.5;
}

.stock-info { 
    display: grid; 
    gap: 10px; 
    margin-top:10px; 
}

.stock-item { 
    display: flex; 
    justify-content: space-between; 
    align-items: center; 
    padding: 10px 15px; 
    background: #f8f9fa; 
    border-radius: 8px; 
}

.stock-item.out-of-stock-item { 
    /* Ingen specifik stil här om kortet redan är utgråat, 
       men stock-count.empty kommer att gälla för siffran */
}

.store-info { 
    flex: 1; 
}

.store-name-small { 
    font-weight: 500; 
    color: #2c3e50; 
}

.store-city { 
    font-size: 0.85rem; 
    color: #6c757d; 
}

.stock-count { 
    background: #28a745; 
    color: white; 
    padding: 5px 12px; 
    border-radius: 20px; 
    font-weight: 600; 
    font-size: 0.9rem; 
}

.stock-count.low { /* För framtida bruk om du vill indikera få i lager */
    background: #ffc107; 
    color: #333; 
} 

.stock-count.empty { 
    background: #adb5bd; 
    color: #fff; 
} 

.loading { 
    text-align: center; 
    padding: 50px; 
    color: white; 
    font-size: 1.2rem; 
}

.spinner { 
    width: 40px; 
    height: 40px; 
    border: 4px solid rgba(255,255,255,0.3); 
    border-top: 4px solid white; 
    border-radius: 50%; 
    animation: spin 1s linear infinite; 
    margin: 0 auto 20px; 
}
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

.error { 
    background: #dc3545; 
    color: white; 
    padding: 15px; 
    border-radius: 10px; 
    margin: 20px 0; 
    text-align: center; 
}

/* --- Stilar för Summeringssektionen --- */
#summary-section {
    background: rgba(255,255,255,0.9); 
    color: #333; 
    padding: 20px 25px; 
    border-radius: 15px; 
    box-shadow: 0 8px 25px rgba(0,0,0,0.08); 
    margin-top: 30px;
}

#summary-section h3 { /* Rubriken "Summering för aktuellt urval" */
    font-size: 1.3rem; 
    color: #2c3e50;   
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #667eea; 
    text-align: left;
}

#summary-section h4 { /* Rubriken "Lagerfördelning per butik" */
    font-size: 1.1rem;
    color: #34495e;
    margin-top: 40px;
    margin-bottom: 10px;
}

.summary-details-list { /* Ny klass för DL-listan i summeringen */
    display: grid;
    grid-template-columns: auto 1fr; 
    gap: 8px 15px; 
    font-size: 0.95em;
    margin-bottom: 20px; 
}

.summary-details-list dt {
    font-weight: 600; 
    color: #555;
    text-align: left; 
}

.summary-details-list dd {
    margin-left: 0;
    text-align: left; 
    font-weight: 500;
}

#summary-section table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 0.9em; 
}

#summary-section th, 
#summary-section td {
    text-align: left;
    padding: 10px 8px; 
    border-bottom: 1px solid #e0e0e0; 
}

#summary-section th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

/* Justera varje kolumnrubrik och cell */
#summary-section th:nth-child(2),
#summary-section td:nth-child(2) { /* Enheter */
  text-align: right;
  white-space: nowrap;
}

#summary-section th:nth-child(3),
#summary-section td:nth-child(3) { /* Andel */
  text-align: right;
  white-space: nowrap;
}

#summary-section th:nth-child(4),
#summary-section td:nth-child(4) { /* Värde */
  text-align: right;
  white-space: nowrap;
}

/* Lite extra färg på hover */
#summary-section tbody tr:hover {
  background-color: #f1f3f5;
}

#summary-section tbody tr:hover {
    background-color: #f1f3f5; 
}

#summary-content p { 
    margin-top: 15px;
    font-size: 0.95em;
    color: #444;
}
/* --- Slut på Summeringsstilar --- */


.footer-credit { 
    text-align: center; 
    color: rgba(255,255,255,0.7); 
    margin-top: 40px; 
    font-size: 0.9rem; 
}

@media (max-width: 768px) { 
    .header h1 { font-size: 2rem; } 
    .store-selector, .results { grid-template-columns: 1fr; } 
}
.header {
  position: relative;
}
.switch-advanced {
  position: absolute;
  top: 8px;   /* eller t.ex. 32px, beroende på din header-höjd */
  right: 24px;
  display: flex;
  align-items: center;
  z-index: 10;
  cursor: pointer;
}