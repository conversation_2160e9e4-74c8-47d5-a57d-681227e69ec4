<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power Lagerstatus</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; font-weight: 700; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1rem; }
        
        .filters-container { background: rgba(255,255,255,0.9); border-radius: 15px; padding: 20px; margin-bottom: 20px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); }
        .filter-group { margin-bottom: 15px; }
        .filter-group h3 { font-size: 1.1rem; color: #555; margin-bottom: 10px; border-bottom: 1px solid #eee; padding-bottom: 5px; }
        .filter-buttons button, #category-buttons button { 
            padding: 8px 12px; margin: 5px; border-radius: 8px; 
            border: 1px solid #ddd; background-color: #f8f9fa; 
            cursor: pointer; transition: all 0.2s ease;
            font-size: 0.9rem;
        }
        .filter-buttons button.active, #category-buttons button.active { 
            background-color: #667eea; color: white; border-color: #5a6fcf;
            font-weight: bold; transform: translateY(-1px); box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .filter-buttons button:hover:not(.active), #category-buttons button:hover:not(.active) { background-color: #e9ecef; }

        .controls { background: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .location-info { display: flex; align-items: center; gap: 15px; margin-bottom: 20px; padding: 15px; background: #f0f2f5; border-radius: 10px; }
        .location-icon { width: 40px; height: 40px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; flex-shrink: 0; }
        #location-status-text { font-weight: 500; }

        .store-selector { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .store-card { padding: 15px; border: 2px solid #e9ecef; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; background: white; }
        .store-card:hover { border-color: #667eea; transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .store-card.selected { border-color: #667eea; background: #f0f5ff; font-weight: bold; }
        .store-name { font-weight: 600; color: #2c3e50; margin-bottom: 5px; }
        .store-address { color: #6c757d; font-size: 0.9rem; }
        .distance { color: #667eea; font-size: 0.8rem; font-weight: 500; margin-top: 5px; }

        .load-button { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 15px 30px; border-radius: 10px; font-size: 1.1rem; font-weight: 600; cursor: pointer; transition: transform 0.2s ease; width: 100%; margin-top:10px;}
        .load-button:hover { transform: translateY(-2px); }
        .load-button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; background: #ccc; }

        .results { display: grid; grid-template-columns: repeat(auto-fill, minmax(330px, 1fr)); gap: 20px; }
        .product-card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 8px 25px rgba(0,0,0,0.08); transition: transform 0.3s ease; }
        .product-card:hover { transform: translateY(-5px); }
        .product-title { font-size: 1.1rem; font-weight: 600; color: #2c3e50; margin-bottom: 15px; line-height: 1.4; }
        .stock-info { display: grid; gap: 10px; }
        .stock-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; background: #f8f9fa; border-radius: 8px; }
        .store-info { flex: 1; }
        .store-name-small { font-weight: 500; color: #2c3e50; }
        .store-city { font-size: 0.85rem; color: #6c757d; }
        .stock-count { background: #28a745; color: white; padding: 5px 12px; border-radius: 20px; font-weight: 600; font-size: 0.9rem; }
        .stock-count.low { background: #ffc107; color: #333; }
        .stock-count.empty { background: #e9ecef; color: #6c757d; }


        .loading { text-align: center; padding: 50px; color: white; font-size: 1.2rem; }
        .spinner { width: 40px; height: 40px; border: 4px solid rgba(255,255,255,0.3); border-top: 4px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .error { background: #dc3545; color: white; padding: 15px; border-radius: 10px; margin: 20px 0; text-align: center; }
        @media (max-width: 768px) { .header h1 { font-size: 2rem; } .store-selector, .results { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Power Lagerstatus</h1>
            <p>Uppdateras automatiskt kl 12, 15, 17 och efter stängning.</p>
        </div>
<div class="filters-container">
            <div class="filter-group">
                <h3>Välj Produktkategori:</h3>
                <div id="category-buttons">
                    <!-- Kategoriknappar genereras här -->
                </div>
            </div>
            <div class="filter-group">
                <h3>Välj Butiker:</h3>
                <div class="filter-buttons">
                    <button id="btn-closest" onclick="setStoreFilter('closest')">Min butik</button>
                    <button id="btn-nearby" onclick="setStoreFilter('nearby')">Närliggande (50km)</button>
                    <button id="btn-user_selected" onclick="setStoreFilter('user_selected')">Anpassat urval</button>
                </div>
            </div>
        </div>
        <div class="controls">
            <div class="location-info">
                <div class="location-icon">📍</div>
                <div id="location-status">
                    <span id="location-status-text">Söker din position...</span>
                </div>
            </div>

            <div class="store-selector" id="store-selector" style="display:none;">
                <!-- Butiker läggs till här dynamiskt -->
            </div>

            <button class="load-button" id="load-button" onclick="loadStockData()">
                Hämta lagerstatus
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div> Hämtar lagerstatus...
        </div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="results" class="results"></div>
    </div>
    

        <script>
        // --- Globala variabler ---
        const stores = [ /* Din fullständiga stores-array här, oförändrad */
            {"storeId": 7152, "namn": "POWER Torpavallen", "adress": "Torpavallsgatan 4D", "postnummer": "41673", "stad": "Göteborg", "region": "Västra Götaland", "lat": 57.6792, "lng": 11.9894},
            {"storeId": 7150, "namn": "POWER Högsbo", "adress": "Lona Knapes gata 1", "postnummer": "42132", "stad": "Västra Frölunda", "region": "Västra Götaland", "lat": 57.6348, "lng": 11.9059},
            {"storeId": 7151, "namn": "POWER Bäckebol", "adress": "Transportgatan 19", "postnummer": "42246", "stad": "Hisings Backa", "region": "Västra Götaland", "lat": 57.7461, "lng": 11.9094},
            {"storeId": 7156, "namn": "POWER Borås", "adress": "Ålgårdsvägen 11", "postnummer": "50630", "stad": "Borås", "region": "Västra Götaland", "lat": 57.7210, "lng": 12.9401},
            {"storeId": 7125, "namn": "POWER Jönköping", "adress": "Solåsvägen 4A", "postnummer": "55303", "stad": "Jönköping", "region": "Jönköping", "lat": 57.7826, "lng": 14.1618},
            {"storeId": 7155, "namn": "POWER Skövde", "adress": "Jonstorpsgatan 3C", "postnummer": "54937", "stad": "Skövde", "region": "Västra Götaland", "lat": 58.3875, "lng": 13.8458},
            {"storeId": 7147, "namn": "POWER Helsingborg Väla", "adress": "Marknadsvägen 5", "postnummer": "25469", "stad": "Ödåkra", "region": "Skåne", "lat": 56.0776, "lng": 12.7441},
            {"storeId": 7170, "namn": "POWER Västerås", "adress": "Hallsta Gårdsgata 7", "postnummer": "72138", "stad": "Västerås", "region": "Västmanland", "lat": 59.6099, "lng": 16.5448},
            {"storeId": 7148, "namn": "POWER Lund", "adress": "Avtalsvägen 2", "postnummer": "22761", "stad": "Lund", "region": "Skåne", "lat": 55.7047, "lng": 13.2900},
            {"storeId": 7149, "namn": "POWER Kristianstad", "adress": "Fundamentgatan 1", "postnummer": "29161", "stad": "Kristianstad", "region": "Skåne", "lat": 56.0280, "lng": 14.1567},
            {"storeId": 7153, "namn": "POWER Linköping", "adress": "Björkgatan 4", "postnummer": "58252", "stad": "Linköping", "region": "Östergötland", "lat": 58.4108, "lng": 15.6214},
            {"storeId": 7154, "namn": "POWER Norrköping", "adress": "Koppargatan 30", "postnummer": "60223", "stad": "Norrköping", "region": "Östergötland", "lat": 58.5877, "lng": 16.1924},
            {"storeId": 7146, "namn": "POWER Malmö Svågertorp", "adress": "Nornegatan 8", "postnummer": "21586", "stad": "Malmö", "region": "Skåne", "lat": 55.5636, "lng": 12.9719},
            {"storeId": 7157, "namn": "POWER Uppsala", "adress": "Stångjärnsgatan 10", "postnummer": "75323", "stad": "Uppsala", "region": "Uppsala", "lat": 59.8586, "lng": 17.6389},
            {"storeId": 7158, "namn": "POWER Örebro", "adress": "Bettorpsgatan 4", "postnummer": "70369", "stad": "Örebro", "region": "Örebro", "lat": 59.2741, "lng": 15.2066},
            {"storeId": 7159, "namn": "POWER Sundsvall", "adress": "Norra Förmansvägen 18", "postnummer": "86341", "stad": "Sundsvall", "region": "Västernorrland", "lat": 62.3908, "lng": 17.3069},
            {"storeId": 7160, "namn": "POWER Gävle", "adress": "Ingenjörsgatan 2", "postnummer": "80293", "stad": "Gävle", "region": "Gävleborg", "lat": 60.6749, "lng": 17.1413},
            {"storeId": 7161, "namn": "POWER Stockholm Kungens Kurva", "adress": "Geometrivägen 1", "postnummer": "14175", "stad": "Kungens Kurva", "region": "Stockholm", "lat": 59.2465, "lng": 17.9414},
            {"storeId": 7162, "namn": "POWER Stockholm Barkarby", "adress": "Herrestavägen 20", "postnummer": "17738", "stad": "Järfälla", "region": "Stockholm", "lat": 59.4142, "lng": 17.8907}
        ];

        let PRODUCTS_TO_MONITOR = []; // Fylls på av initializeApp()
        let allAvailableCategories = []; 
        let userLocation = null;
        let selectedStores = []; 
        let currentStoreFilterMode = 'closest'; 
        let activeStoreFilterButton = null;
        let currentSelectedCategory = null; 
        let activeCategoryButton = null;

        const locationStatusDiv = document.getElementById('location-status-text');
        const storeSelectorDiv = document.getElementById('store-selector');
        const loadButton = document.getElementById('load-button'); // Behåll namnet loadButton
        const loadingDiv = document.getElementById('loading');
        const errorDiv = document.getElementById('error');
        const resultsDiv = document.getElementById('results');
        const categoryButtonsDiv = document.getElementById('category-buttons');

        // --- API URL (BYT DENNA TILL DIN AKTUELLA NGROK URL) ---
        const NGROK_BASE_URL = 'https://bb43-31-208-30-3.ngrok-free.app'; 
        // const baseApiUrl = 'http://***********:5000/api/current_stock';// Exempel, byt!
        // ----------------------------------------------------

        async function initializeApp() {
            console.log("initializeApp called");
            try {
                const productsApiUrl = `${NGROK_BASE_URL}/api/products`;
                console.log("Hämtar produktlista från:", productsApiUrl);
                const response = await fetch(productsApiUrl, {
                    headers: { 'ngrok-skip-browser-warning': 'true' }
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Kunde inte hämta produktlistan: ${response.status}. Svar: ${errorText.substring(0,100)}`);
                }
                const productsFromApi = await response.json();
                console.log("Produktlista mottagen från API:", productsFromApi.length, "produkter");

                if (productsFromApi && productsFromApi.length > 0) {
                    PRODUCTS_TO_MONITOR = productsFromApi.map(p => ({
                        productId: p.productId.toString(),
                        title: p.title,
                        category: p.categoryName 
                    }));
                    allAvailableCategories = [...new Set(PRODUCTS_TO_MONITOR.map(p => p.category).filter(cat => cat))].sort();
                } else {
                    console.warn("Produktlistan från API var tom eller ogiltig.");
                }
            } catch (err) {
                console.error("FEL vid hämtning av produktlista:", err);
                errorDiv.textContent = `Kunde inte ladda den initiala produktlistan: ${err.message}`;
                errorDiv.style.display = 'block';
            }
            renderCategoryButtons(); 
            selectCategory(null); // Förvälj "Alla Produkter"
            getUserLocation(); // Starta positions- och butikslogik       
        }

        function calculateDistance(lat1, lng1, lat2, lng2) { /* ... (oförändrad) ... */ 
            const R = 6371; const dLat = (lat2 - lat1) * Math.PI / 180; const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); return R * c;
        }

        function updateActiveButton(buttonClass, newActiveButtonId, isCategory = false) {
            const buttons = document.querySelectorAll(`.${buttonClass} button`);
            buttons.forEach(btn => btn.classList.remove('active'));
            const activeButton = document.getElementById(newActiveButtonId);
            if (activeButton) {
                activeButton.classList.add('active');
                if (!isCategory) activeStoreFilterButton = activeButton;
                // För kategorier hanterar vi den aktiva knappen i renderCategoryButtons baserat på currentSelectedCategory
            }
        }

        function getUserLocation() { /* ... (som tidigare, anropar setStoreFilter) ... */
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        userLocation = { lat: position.coords.latitude, lng: position.coords.longitude };
                        locationStatusDiv.textContent = `Position hittad.`;
                        setStoreFilter('closest', true); 
                    },
                    (error) => {
                        console.error('Geolocation error:', error);
                        locationStatusDiv.textContent = 'Kunde inte hitta position. Välj butiker manuellt.';
                        setStoreFilter('user_selected', false); 
                    }
                );
            } else {
                locationStatusDiv.textContent = 'Geolocation stöds inte. Välj butiker manuellt.';
                setStoreFilter('user_selected', false);
            }
        }
        
        function setStoreFilter(mode, shouldLoadData = true) { /* ... (som tidigare) ... */
            currentStoreFilterMode = mode;
            updateActiveButton('filter-buttons', `btn-${mode}`);
            selectedStores = [];
            if (!userLocation && (mode === 'closest' || mode === 'nearby')) {
                locationStatusDiv.textContent = 'Position behövs. Välj manuellt.';
                currentStoreFilterMode = 'user_selected'; updateActiveButton('filter-buttons', 'btn-user_selected');
            }
            if (currentStoreFilterMode === 'closest' && userLocation) {
                let closest = stores.reduce((prev, curr) => (calculateDistance(userLocation.lat, userLocation.lng, curr.lat, curr.lng) < calculateDistance(userLocation.lat, userLocation.lng, prev.lat, prev.lng) ? curr : prev));
                selectedStores = [closest.storeId];
                locationStatusDiv.textContent = `Visar för närmaste: ${closest.namn} (${calculateDistance(userLocation.lat,userLocation.lng,closest.lat,closest.lng).toFixed(1)} km)`;
            } else if (currentStoreFilterMode === 'nearby' && userLocation) {
                const nearbyRadiusKm = 50;
                selectedStores = stores.filter(s => calculateDistance(userLocation.lat, userLocation.lng, s.lat, s.lng) <= nearbyRadiusKm).map(s => s.storeId);
                locationStatusDiv.textContent = `Visar ${selectedStores.length} närliggande butiker (inom ${nearbyRadiusKm}km).`;
            } else { 
                locationStatusDiv.textContent = 'Anpassat urval: Välj butiker i listan.';
            }
            displayStores(); 
            updateLoadButton();
            if (shouldLoadData && selectedStores.length > 0) loadStockData();
            else if (shouldLoadData && selectedStores.length === 0) {
                resultsDiv.innerHTML = `<div style="grid-column: 1/-1; text-align: center; color: white; padding: 50px;"><h3>Inga butiker matchade filtret</h3></div>`;
            }
        }

        function displayStores() { /* ... (som tidigare) ... */
            if (currentStoreFilterMode !== 'user_selected') {
                storeSelectorDiv.style.display = 'none'; return;
            }
            storeSelectorDiv.style.display = 'grid';
            let sortedStores = [...stores];
            if (userLocation) {
                sortedStores.forEach(s => s.distance = calculateDistance(userLocation.lat, userLocation.lng, s.lat, s.lng));
                sortedStores.sort((a, b) => a.distance - b.distance);
            }
            if (selectedStores.length === 0 && userLocation && sortedStores.length > 0 && currentStoreFilterMode === 'user_selected') {
                 selectedStores = sortedStores.slice(0, Math.min(3, sortedStores.length)).map(s => s.storeId);
            }
            storeSelectorDiv.innerHTML = ''; 
            sortedStores.forEach(store => {
                const storeCard = document.createElement('div');
                storeCard.className = `store-card ${selectedStores.includes(store.storeId) ? 'selected' : ''}`;
                storeCard.dataset.storeId = store.storeId;
                storeCard.onclick = () => toggleStore(store.storeId);
                let dText = store.distance ? `<div class="distance">${store.distance.toFixed(1)} km</div>` : '';
                storeCard.innerHTML = `<div class="store-name">${store.namn}</div><div class="store-address">${store.adress}, ${store.stad}</div>${dText}`;
                storeSelectorDiv.appendChild(storeCard);
            });
            updateLoadButton();
        }

        function toggleStore(storeId) { /* ... (som tidigare) ... */
            const index = selectedStores.indexOf(storeId);
            if (index > -1) selectedStores.splice(index, 1); else selectedStores.push(storeId);
            displayStores();
            if (selectedStores.length > 0) loadStockData();
            else {
                resultsDiv.innerHTML = `<div style="grid-column: 1/-1; text-align:center; color:white; padding:50px;"><h3>Välj butiker.</h3></div>`;
                updateLoadButton();
            }
        }

        function updateLoadButton() { /* ... (som tidigare, men byt 'button' till 'loadButton') ... */
            if (currentStoreFilterMode === 'closest') loadButton.textContent = selectedStores.length > 0 ? `Uppdatera för ${stores.find(s=>s.storeId === selectedStores[0])?.namn || 'närmaste'}` : 'Ingen närmaste butik';
            else if (currentStoreFilterMode === 'nearby') loadButton.textContent = `Uppdatera för närliggande (${selectedStores.length} st)`;
            else loadButton.textContent = selectedStores.length === 0 ? 'Välj butiker och klicka här' : `Ladda lagerstatus (${selectedStores.length} valda)`; // Ändrade texten lite
            loadButton.disabled = selectedStores.length === 0;
        }
        
        function renderCategoryButtons() { /* ... (som tidigare) ... */
            categoryButtonsDiv.innerHTML = '';
            const allBtn = document.createElement('button');
            allBtn.textContent = 'Alla Produkter';
            allBtn.id = 'btn-cat-null'; // Ge ID för active state
            allBtn.onclick = () => selectCategory(null);
            if (currentSelectedCategory === null) allBtn.classList.add('active');
            categoryButtonsDiv.appendChild(allBtn);

            allAvailableCategories.forEach(category => {
                const btn = document.createElement('button');
                btn.textContent = category;
                btn.id = `btn-cat-${category.replace(/\s|&/g, '-')}`; // Skapa ett unikt ID
                btn.onclick = () => selectCategory(category);
                if (currentSelectedCategory === category) btn.classList.add('active');
                categoryButtonsDiv.appendChild(btn);
            });
        }

        function selectCategory(categoryName) { /* ... (som tidigare) ... */
            currentSelectedCategory = categoryName;
            // Uppdatera aktiva knappen visuellt
            const catButtons = categoryButtonsDiv.querySelectorAll('button');
            catButtons.forEach(b => b.classList.remove('active'));
            if (categoryName) {
                document.getElementById(`btn-cat-${categoryName.replace(/\s|&/g, '-')}`)?.classList.add('active');
            } else {
                document.getElementById('btn-cat-null')?.classList.add('active');
            }

            if (selectedStores.length > 0 || currentStoreFilterMode !== 'user_selected') {
                loadStockData();
            } else {
                resultsDiv.innerHTML = `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Välj butiker för kategorin '${categoryName || "Alla produkter"}'</h3></div>`;
            }
        }

        async function loadStockData() { /* ... (din senaste fungerande med ngrok-URL och kategorifilter) ... */
            console.log(`--- loadStockData called (Kat: ${currentSelectedCategory || 'Alla'}, Butiksfilter: ${currentStoreFilterMode}, Butiker: ${selectedStores.join(',')}) ---`);
            loading.style.display = 'block'; errorDiv.style.display = 'none'; resultsDiv.innerHTML = '';
            if (selectedStores.length === 0) {
                console.warn(`Inga butiker att ladda för.`);
                resultsDiv.innerHTML = `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Välj butiker.</h3></div>`;
                loading.style.display = 'none'; updateLoadButton(); return;
            }
            
            // Använd ALLA produkt-ID:n från PRODUCTS_TO_MONITOR. API:et filtrerar på kategori.
            const productIdsParam = PRODUCTS_TO_MONITOR.map(p => p.productId).join(',');
            const storeIdsParam = selectedStores.join(',');
            
            const baseApiUrl = `${NGROK_BASE_URL}/api/current_stock`; 
            
            let queryParams = [];
            // Vi behöver inte skicka product_ids om API:et inte använder det för att ytterligare filtrera när kategori ges.
            // Om ditt API alltid returnerar ALLA produkter för den valda kategorin, oavsett product_ids här, kan vi förenkla.
            // Men för nu, skicka med dem.
            // if (productIdsParam) queryParams.push(`product_ids=${productIdsParam}`); // Kanske onödig om kategori skickas
            if (storeIdsParam) queryParams.push(`store_ids=${storeIdsParam}`);
            if (currentSelectedCategory) queryParams.push(`category=${encodeURIComponent(currentSelectedCategory)}`);
                        
            const fullApiUrl = `${baseApiUrl}${queryParams.length > 0 ? '?' : ''}${queryParams.join('&')}`;
            console.log("Anropar API:", fullApiUrl);

            try {
                const response = await fetch(fullApiUrl, { headers: { 'ngrok-skip-browser-warning': 'true' } });
                console.log(`Svar från API status: ${response.status}`);
                if (!response.ok) {
                    const errorText = await response.text(); 
                    console.error(`API anrop misslyckades: ${response.status} ${response.statusText}`, errorText);
                    throw new Error(`API: ${response.status}. ${errorText.substring(0,100)}`);
                }
                const apiData = await response.json(); 
                console.log("Data mottagen:", apiData.length, "lagerstatusrader");
                
                const productsWithStock = [];
                const productMap = new Map();

                // Bygg upp produktlistan baserat på PRODUCTS_TO_MONITOR för att få rätt titlar och kategorier
                // och sedan populera deras 'stocks' från apiData.
                const productsToConsider = currentSelectedCategory 
                    ? PRODUCTS_TO_MONITOR.filter(p => p.category === currentSelectedCategory)
                    : PRODUCTS_TO_MONITOR;

                productsToConsider.forEach(prodSource => {
                    productMap.set(prodSource.productId.toString(), {
                        productId: prodSource.productId.toString(),
                        title: prodSource.title,
                        category: prodSource.category,
                        stocks: []
                    });
                });
                
                apiData.forEach(item => {
                    const productIdStr = item.productId.toString();
                    if (productMap.has(productIdStr)) { // Se till att produkten från API:et är en vi bryr oss om
                        const storeDetails = stores.find(s => s.storeId == item.storeId);
                        const storeName = storeDetails ? storeDetails.namn : `Butik ${item.storeId}`;
                        const storeCity = storeDetails ? storeDetails.stad : '';
                        productMap.get(productIdStr).stocks.push({
                            store: { namn: storeName, stad: storeCity, storeId: item.storeId }, 
                            stock: item.stockCount
                        });
                    }
                });

                // Konvertera map till array för displayResults
                productsWithStock.push(...productMap.values());
                
                console.log("Bearbetad productsWithStock:", productsWithStock);
                displayResults(productsWithStock);

            } catch (err) {
                console.error("Fel i loadStockData:", err);
                let msg = err.message;
                if (err instanceof SyntaxError) msg = "Svar från servern var inte giltig JSON.";
                errorDiv.textContent = `Kunde inte hämta lager: ${msg}`;
                errorDiv.style.display = 'block';
                resultsDiv.innerHTML = `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Fel</h3><p>${msg}</p></div>`;
            } finally {
                loading.style.display = 'none';
                console.log("--- loadStockData() (Pi API) färdig ---");
            }
        }

        function displayResults(products) { /* ... (som tidigare, men se till att den visar product.category om du vill) ... */
            resultsDiv.innerHTML = ''; 
            let productsActuallyShown = 0;

            // Produkten är redan filtrerad på kategori av loadStockData ELLER av API:et.
            // Vi behöver inte filtrera på currentSelectedCategory här igen om products-listan är korrekt.

            products.forEach(product => { // products här ÄR productsWithStock
                // Filtrera bort produkter som inte har någon lagerinformation alls för de valda butikerna,
                // eller om alla butiker för produkten har 0 i lager.
                const relevantStocks = product.stocks.filter(stock => stock.stock > 0);

                if (relevantStocks.length > 0) {
                    const productCard = document.createElement('div');
                    productCard.className = 'product-card';
                    const stockItems = relevantStocks.map(stock => `
                        <div class="stock-item">
                            <div class="store-info">
                                <div class="store-name-small">${stock.store.namn}</div>
                                <div class="store-city">${stock.store.stad}</div>
                            </div>
                            <div class="stock-count">${stock.stock} st</div>
                        </div>
                    `).join('');
                
                    productCard.innerHTML = `
                        <div class="product-title">${product.title}</div> 
                        <div style="font-size:0.8em; color:#555; margin-bottom:10px;">Kategori: ${product.category}</div>
                        <div class="stock-info">
                            ${stockItems}
                        </div>
                    `;
                    resultsDiv.appendChild(productCard);
                    productsActuallyShown++;
                }
            });
            if (productsActuallyShown === 0) {
                 resultsDiv.innerHTML = `<div style="grid-column: 1/-1; text-align: center; color: white; padding: 50px;"><h3>Inga produkter i lager</h3><p>Inga produkter matchade dina filter med lager > 0.</p></div>`;
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            initializeApp(); 
        });
    </script>
</body>
</html>