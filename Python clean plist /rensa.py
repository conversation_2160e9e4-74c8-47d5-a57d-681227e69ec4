import json

# Ladda in din produktlista
with open('produktlista.json', encoding='utf-8') as f:
    produkter = json.load(f)

# Lista med modeller du vill ta bort (case-insensitive)
ta_bort = ["iphone se", "iphone 13", "iphone 14"]

# Filtrera bort oönskade produkter
filtrerad_lista = [
    prod for prod in produkter
    if not any(model in prod.get("title", "").lower() for model in ta_bort)
]

# Spara till ny fil
with open('alla_produkter_utan_gamla_iphones.json', 'w', encoding='utf-8') as f:
    json.dump(filtrerad_lista, f, ensure_ascii=False, indent=2)

print(f"Klart! Nu har du {len(filtrerad_lista)} produkter kvar utan gamla iPhones.") 