import plistlib
import re
import os

def categorize_product(title):
    """
    Kategoriserar en produkt baserat på dess titel.
    Returnerar kategorin som sträng.
    """
    title_lower = title.lower()
    
    # iPhone
    if 'iphone' in title_lower:
        return 'iphone'
    
    # iPad
    if 'ipad' in title_lower:
        return 'ipad'
    
    # Mac (MacBook, iMac, Mac Studio, Mac Pro, Mac mini)
    if any(mac in title_lower for mac in ['macbook', 'imac', 'mac studio', 'mac pro', 'mac mini']):
        return 'mac'
    
    # Apple Watch
    if 'watch' in title_lower:
        return 'watch'
    
    # AirPods
    if 'airpods' in title_lower:
        return 'airpods'
    
    # HomePod
    if 'homepod' in title_lower:
        return 'homepod'
    
    # Apple TV
    if 'apple tv' in title_lower or 'appletv' in title_lower:
        return 'appletv'
    
    # Tillbehör - allt annat (sladdar, adaptrar, cases, etc.)
    return 'tillbehor'

def categorize_apple_products(input_file):
    """
    Läser in rensad.plist och delar upp produkterna i kategorier.
    Sparar varje kategori som en separat plist-fil.
    """
    try:
        # Läs in den rensade produktlistan
        with open(input_file, 'rb') as f:
            products = plistlib.load(f)
        
        print(f"Totalt antal produkter att kategorisera: {len(products)}")
        
        # Skapa kategorier
        categories = {
            'iphone': [],
            'ipad': [],
            'mac': [],
            'watch': [],
            'airpods': [],
            'homepod': [],
            'appletv': [],
            'tillbehor': []
        }
        
        # Kategorisera varje produkt
        for product in products:
            title = product.get('title', '')
            category = categorize_product(title)
            categories[category].append(product)
        
        # Visa statistik
        print("\nKategorisering klar:")
        print("-" * 40)
        for category, items in categories.items():
            if items:  # Visa bara kategorier som har produkter
                print(f"{category.capitalize()}: {len(items)} produkter")
        
        # Spara varje kategori som separat fil
        saved_files = []
        for category, items in categories.items():
            if items:  # Spara bara kategorier som har produkter
                filename = f"{category}.plist"
                
                with open(filename, 'wb') as f:
                    plistlib.dump(items, f)
                
                saved_files.append(filename)
                print(f"Sparade {len(items)} produkter i {filename}")
        
        print(f"\nTotalt {len(saved_files)} filer skapades:")
        for filename in saved_files:
            print(f"  - {filename}")
            
        # Visa exempel på tillbehör för kontroll
        if categories['tillbehor']:
            print(f"\nExempel på tillbehör ({min(5, len(categories['tillbehor']))} av {len(categories['tillbehor'])}):")
            for i, product in enumerate(categories['tillbehor'][:5]):
                print(f"  - {product.get('title', 'Okänd titel')}")
        
    except FileNotFoundError:
        print(f"Kunde inte hitta filen: {input_file}")
        print("Se till att du har kört det första skriptet och skapat rensad.plist")
    except Exception as e:
        print(f"Ett fel uppstod: {e}")

# Kör skriptet
if __name__ == "__main__":
    input_filename = "rensad.plist"
    
    print("Kategoriserar Apple-produkter...")
    print("=" * 50)
    
    categorize_apple_products(input_filename)