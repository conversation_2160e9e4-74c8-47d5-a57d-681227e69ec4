import plistlib
import re

def should_remove_iphone(title):
    """
    Kontrollerar om en iPhone-produkt ska tas bort baserat på titel.
    Tar bort iPhone 12, 13, 14, och SE (alla med Lightning).
    Behåller iPhone 15 och 16 (USB-C).
    """
    # Gör titeln lowercase för enklare matchning
    title_lower = title.lower()
    
    # Om det inte är en iPhone, behåll den
    if 'iphone' not in title_lower:
        return False
    
    # Mönster för äldre iPhones som ska tas bort
    patterns_to_remove = [
        r'iphone\s+12',    # iPhone 12
        r'iphone\s+13',    # iPhone 13  
        r'iphone\s+14',    # iPhone 14
        r'iphone\s+se',    # iPhone SE
    ]
    
    # Kontrollera om någon av de äldre modellerna matchar
    for pattern in patterns_to_remove:
        if re.search(pattern, title_lower):
            return True
    
    return False

def clean_apple_products(input_file, output_file):
    """
    <PERSON><PERSON><PERSON> in produktlistan, rensar bort äldre iPhones och sparar till ny fil.
    """
    try:
        # Läs in den ursprungliga plist-filen
        with open(input_file, 'rb') as f:
            products = plistlib.load(f)
        
        print(f"Ursprungligt antal produkter: {len(products)}")
        
        # Filtrera bort äldre iPhones
        cleaned_products = []
        removed_count = 0
        
        for product in products:
            title = product.get('title', '')
            
            if should_remove_iphone(title):
                print(f"Tar bort: {title}")
                removed_count += 1
            else:
                cleaned_products.append(product)
        
        print(f"Antal borttagna produkter: {removed_count}")
        print(f"Antal kvarvarande produkter: {len(cleaned_products)}")
        
        # Spara den rensade listan
        with open(output_file, 'wb') as f:
            plistlib.dump(cleaned_products, f)
        
        print(f"Rensad lista sparad som: {output_file}")
        
    except FileNotFoundError:
        print(f"Kunde inte hitta filen: {input_file}")
    except Exception as e:
        print(f"Ett fel uppstod: {e}")

# Kör skriptet
if __name__ == "__main__":
    input_filename = "produktlista.json"  # Trots namnet verkar det vara en plist
    output_filename = "rensad.plist"
    
    clean_apple_products(input_filename, output_filename)