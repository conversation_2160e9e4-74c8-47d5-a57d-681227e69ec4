# api_server.py
import sqlite3
import json 
import time 
import requests
from flask import Flask, jsonify, request 
from flask_cors import CORS 
from datetime import datetime, timedelta

app = Flask(__name__)
CORS(app) # Håll det enkelt med wildcard under felsökning
DATABASE_NAME = 'power_stock.db' 
FLASK_PORT = 5000

def query_db(query, args=(), one=False):
    try:
        conn = sqlite3.connect(f"file:{DATABASE_NAME}?mode=ro", uri=True) # Öppna i read-only mode för säkerhet i API:et
        conn.row_factory = sqlite3.Row 
        cur = conn.cursor()
        cur.execute(query, args)
        rv = cur.fetchall()
        conn.close()
        return (dict(rv[0]) if rv else None) if one else [dict(row) for row in rv]
    except sqlite3.Error as e:
        print(f"Database query error: {e}\nQuery: {query}\nArgs: {args}")
        return None 

# --- ÄNDPUNKTER ---

@app.route('/')
def index():
    return "Power Stock API är igång!"

@app.route('/api/products', methods=['GET'])
def get_products():
    # Anta att en 'active'-kolumn finns (lägg till den i DB om den saknas: ALTER TABLE products ADD COLUMN active INTEGER DEFAULT 1;)
    products_data = query_db("SELECT productId, title, categoryName, ean, sku, price FROM products WHERE active = 1 ORDER BY categoryName, title ASC")
    if products_data is None: return jsonify({"error": "DB-fel vid hämtning av produkter."}), 500
    return jsonify(products_data)

@app.route('/api/current_stock', methods=['GET'])
def get_current_stock():
    print("--- Anrop till /api/current_stock ---")
    overall_start_time = time.time() # Tid för hela requesten

    product_ids_str = request.args.get('product_ids') # Behåll för flexibilitet, men används inte om kategori finns
    store_ids_str = request.args.get('store_ids')
    category_name_str = request.args.get('category')

    base_query = """
        SELECT 
            h.productId, p.title AS productTitle, p.categoryName AS productCategory, 
            h.storeId, h.stockCount, h.timestamp,
            p.ean, p.sku, p.price /* LÄGG TILL DESSA OM DU VILL HA DEM DIREKT HÄR */
        FROM stock_levels_history h
        JOIN products p ON h.productId = p.productId
        WHERE h.log_id = (
            SELECT MAX(slh_inner.log_id) 
            FROM stock_levels_history slh_inner
            WHERE slh_inner.productId = h.productId AND slh_inner.storeId = h.storeId
        )
    """
    conditions = []
    params = []

    if category_name_str and category_name_str.lower() != "alla produkter" and category_name_str.lower() != "all":
        conditions.append("p.categoryName = ?")
        params.append(category_name_str)
    
    if store_ids_str:
        store_ids_list = [sid.strip() for sid in store_ids_str.split(',') if sid.strip()]
        if store_ids_list:
            conditions.append(f"h.storeId IN ({','.join(['?']*len(store_ids_list))})")
            params.extend(store_ids_list)
    
    # Om du fortfarande vill kunna filtrera på specifika produkt-ID:n (även om kategori är satt)
    # kan du lägga tillbaka denna logik. Men om kategorifiltret är primärt, kanske den inte behövs.
    # if product_ids_str:
    #     product_ids_list = [pid.strip() for pid in product_ids_str.split(',') if pid.strip()]
    #     if product_ids_list:
    #         conditions.append(f"h.productId IN ({','.join(['?']*len(product_ids_list))})")
    #         params.extend(product_ids_list)

    if conditions:
        base_query += " AND " + " AND ".join(conditions)
    base_query += " ORDER BY p.categoryName, p.title, h.storeId;"
    
    print(f"Query för current_stock: {base_query}")
    print(f"Params för current_stock: {params}")

    db_query_start_time = time.time()
    stock_data = query_db(base_query, tuple(params))
    db_query_end_time = time.time()
    print(f"DB query tog: {db_query_end_time - db_query_start_time:.4f} sekunder.")
    
    if stock_data is None: 
        print(f"--- /api/current_stock slut (DB Fel). Total tid: {time.time() - overall_start_time:.4f}s ---")
        return jsonify({"error": "Databasfel vid current_stock."}), 500
    
    print(f"--- /api/current_stock slut. Returnerar {len(stock_data)} rader. Total tid: {time.time() - overall_start_time:.4f}s ---")
    return jsonify(stock_data)

# --- NYA ANALYS-ÄNDPUNKTER ---

@app.route('/api/product_history/<product_id>', methods=['GET'])
def get_product_history(product_id):
    """Hämtar detaljerad historik för en specifik produkt"""
    print(f"--- Anrop till /api/product_history/{product_id} ---")

    # Hämta antal dagar från query parameter (default 30)
    days = request.args.get('days', 30, type=int)
    if days > 365:  # Begränsa till max 1 år
        days = 365

    # Beräkna startdatum
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d 00:00:00')

    # Först, kontrollera att produkten finns
    product_query = "SELECT productId, title, categoryName, ean, sku, price FROM products WHERE productId = ? AND active = 1"
    product_data = query_db(product_query, (product_id,), one=True)

    if not product_data:
        return jsonify({"error": f"Produkt {product_id} hittades inte eller är inaktiv"}), 404

    # Hämta historikdata
    history_query = """
        SELECT
            h.productId,
            h.storeId,
            h.stockCount,
            h.timestamp,
            LAG(h.stockCount) OVER (
                PARTITION BY h.productId, h.storeId
                ORDER BY h.timestamp
            ) as previous_stock
        FROM stock_levels_history h
        WHERE h.productId = ?
        AND h.timestamp >= ?
        ORDER BY h.timestamp DESC
    """

    history_data = query_db(history_query, (product_id, start_date))

    if history_data is None:
        return jsonify({"error": "Databasfel vid hämtning av historik"}), 500

    # Beräkna förändringar och statistik
    processed_history = []
    stock_changes = []

    for entry in history_data:
        change = 0
        if entry['previous_stock'] is not None:
            change = entry['stockCount'] - entry['previous_stock']

        processed_entry = {
            'date': entry['timestamp'][:10],  # YYYY-MM-DD
            'time': entry['timestamp'][11:16] if len(entry['timestamp']) > 10 else '00:00',  # HH:MM
            'store_id': entry['storeId'],
            'stock_before': entry['previous_stock'] or 0,
            'stock_after': entry['stockCount'],
            'change': change,
            'timestamp': entry['timestamp']
        }

        processed_history.append(processed_entry)

        if change != 0:
            stock_changes.append({
                'change': change,
                'timestamp': entry['timestamp'],
                'store_id': entry['storeId']
            })

    # Beräkna statistik
    total_increases = sum(c['change'] for c in stock_changes if c['change'] > 0)
    total_decreases = abs(sum(c['change'] for c in stock_changes if c['change'] < 0))
    restock_events = len([c for c in stock_changes if c['change'] > 0])

    # Hämta aktuell lagerstatus
    current_stock_query = """
        SELECT h.storeId, h.stockCount
        FROM stock_levels_history h
        WHERE h.productId = ?
        AND h.log_id = (
            SELECT MAX(slh_inner.log_id)
            FROM stock_levels_history slh_inner
            WHERE slh_inner.productId = h.productId AND slh_inner.storeId = h.storeId
        )
        ORDER BY h.storeId
    """

    current_stock = query_db(current_stock_query, (product_id,))

    response_data = {
        'product': product_data,
        'current_stock': current_stock or [],
        'history': processed_history,
        'statistics': {
            'total_entries': len(history_data),
            'total_increases': total_increases,
            'total_decreases': total_decreases,
            'net_change': total_increases - total_decreases,
            'restock_events': restock_events,
            'period_days': days
        }
    }

    print(f"--- /api/product_history/{product_id} slut. Returnerar {len(history_data)} historikposter ---")
    return jsonify(response_data)

@app.route('/api/inventory_turnover', methods=['GET'])
def get_inventory_turnover():
    # Denna funktion ser bra ut, men är komplex. Den förutsätter att historiken är komplett.
    # För nu, returnera dummy-data så att frontend inte kraschar.
    # return jsonify({"total_in": 123, "total_out": 456})
    # Den riktiga koden från Gemini:
    period = request.args.get('period', 'week')
    today = datetime.now()
    if period == 'yesterday': start_date = (today - timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')
    elif period == 'week': start_date = (today - timedelta(days=today.weekday())).strftime('%Y-%m-%d 00:00:00')
    elif period == 'month': start_date = today.replace(day=1).strftime('%Y-%m-%d 00:00:00')
    else: return jsonify({"error": "Ogiltig period"}), 400
    query = """...""" # Din befintliga query är OK men kan vara långsam. Vi kan optimera den senare.
    # För nu, låt oss returnera dummy-data.
    return jsonify({"total_in": "...", "total_out": "..."})


# Öppna databasen i read-only ('ro') mode för att förhindra låsning vid SELECT-frågor
# uri=True behövs för att kunna skicka med mode-parametern.
conn = sqlite3.connect(f"file:{DATABASE_NAME}?mode=ro", uri=True)

if __name__ == '__main__':
    print(f"Startar Flask API server på port {FLASK_PORT}...")
    app.run(host='0.0.0.0', port=FLASK_PORT, debug=True)